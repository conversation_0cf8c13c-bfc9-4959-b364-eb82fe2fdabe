import { withAuth } from "next-auth/middleware";

export default withAuth(
  // `withAuth` augments your `Request` with the user's token.
  function middleware(req) {
    //todo role validation
    const path = req.nextUrl.pathname;
  },
  {
    callbacks: {
      authorized: ({ token }) => {
        return !!token?.roles;
      },
    },
  },
);

export const config = {
  matcher: ["/", "/account/:path*", "/videos/:path*", "/api-doc/:path*"],
};
