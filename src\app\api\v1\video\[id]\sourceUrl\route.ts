import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { TRPCError } from "@trpc/server";
import dayjs from "dayjs";
import type { NextRequest } from "next/server";
import { env } from "~/env";
import { s3Client } from "~/server/api/routers/aws";
import { getCloudFrontSignedUrl } from "~/server/utils/cloudFront";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/sourceUrl:
 *   get:
 *     description: Get video source url.
 *     tags: [Video playback]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *        200:
 *          description: Video source url
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  videoUrl:
 *                    type: string
 *                  cacheUrl:
 *                    type: string
 *                  expiry:
 *                    type: string
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;
  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  try {
    const { tokenPayload, token } = await checkToken(request);

    const video = await getVideoByIdForPermissionCheck(
      id,
      tokenPayload.roles,
      token,
    );

    if (!video?.fullFilename) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    const getObjectCommand = new GetObjectCommand({
      Bucket: env.AWS_BUCKET_NAME,
      Key: video.fullFilename,
    });

    const videoPresignedUrl = await getSignedUrl(s3Client, getObjectCommand, {
      expiresIn: 3600 * 24,
    });

    const cacheUrl = getCloudFrontSignedUrl(video.fullFilename);

    return Response.json({
      videoUrl: videoPresignedUrl,
      cacheUrl,
      expiry: dayjs().add(1, "day").toISOString(),
    });
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
