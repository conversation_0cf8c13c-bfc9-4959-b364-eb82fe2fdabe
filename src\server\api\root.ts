import { videoRouter } from "~/server/api/routers/video";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";
import { awsRouter } from "./routers/aws";
import type { inferRouterInputs, inferRouterOutputs } from "@trpc/server";
import { accountRouter } from "./routers/account";
import { ptaRouter } from "./routers/pta";
import { seedRouter } from "./routers/seed";
import { azureRouter } from "./routers/azure";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  video: videoRouter,
  aws: awsRouter,
  account: accountRouter,
  pta: ptaRouter,
  seed: seedRouter,
  azure: azureRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);

/**
 * Inference helper for inputs.
 *
 * @example type HelloInput = RouterInputs['example']['hello']
 */
export type RouterInputs = inferRouterInputs<AppRouter>;

/**
 * Inference helper for outputs.
 *
 * @example type HelloOutput = RouterOutputs['example']['hello']
 */
export type RouterOutputs = inferRouterOutputs<AppRouter>;
