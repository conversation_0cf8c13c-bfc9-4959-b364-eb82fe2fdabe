export const downloadFileFromUrl = (url: string, filename: string) => {
  const link = document.createElement("a");
  link.href = url;
  link.target = "_blank";
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // const response = await fetch(url);
  // const blob = await response.blob();
  // const blobUrl = URL.createObjectURL(blob);

  // const link = document.createElement("a");
  // link.href = blobUrl;
  // link.download = filename;
  // document.body.appendChild(link);
  // link.click();
  // document.body.removeChild(link);

  // URL.revokeObjectURL(blobUrl);
};

export const multipartUploadSize = 100 * 1024 * 1024; // 100MB, if file size is greater than this, use multipart upload
export const fileChunkSize = 10 * 1024 * 1024; // 10MB
