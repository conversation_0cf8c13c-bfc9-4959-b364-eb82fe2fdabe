import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import { checkToken } from "~/server/utils/permissions";
import { db } from "~/server/db";
import { and, eq, ne } from "drizzle-orm";
import { UserRole, type VideoStatus } from "~/lib/enums";
import { videos } from "~/server/db/schema";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/status:
 *   put:
 *     description: |
 *       Update a video status.
 *
 *       **Roles required:** api, api_modify, admin / analyst
 *     tags: [Update video]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [Raw, Trimmed, Tagged_by_AI, Tagged, Review, Analysed]
 *     responses:
 *       200:
 *         description: Video summary
 *         content:
 *           application/json:
 *              schema:
 *                type: object
 *                properties:
 *                 id:
 *                   type: string
 *                 title:
 *                   type: string
 *                 sport:
 *                   type: string
 *                 permission:
 *                   type: string
 *                 isDraft:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 status:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                 updatedAt:
 *                   type: string
 */
interface UpdateVideoStatus {
  status: VideoStatus;
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;

  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  const body = (await request.json()) as UpdateVideoStatus;

  try {
    const { tokenPayload, token } = await checkToken(request, [
      UserRole.api_modify,
    ]);
    await getVideoByIdForPermissionCheck(id, tokenPayload.roles, token);
    // update video status

    const res = await db
      .update(videos)
      .set({
        status: body.status,
      })
      .where(and(eq(videos.id, id), ne(videos.status, body.status)));

    const updatedVideo = await db.query.videos.findFirst({
      where: (fields) => eq(fields.id, id),
      columns: {
        id: true,
        title: true,
        sport: true,
        permission: true,
        isDraft: true,
        isDeleted: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({ ...updatedVideo, rowsAffected: res.rowsAffected });
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
