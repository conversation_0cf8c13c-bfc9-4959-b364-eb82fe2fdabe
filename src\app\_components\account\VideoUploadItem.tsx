import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";
import { useRef, useState } from "react";
import toast from "react-hot-toast";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { SelectWithLabel } from "~/components/ui/select";
import { useRunOnce } from "~/hooks/useEffectOnce";
import type { Sport, VideoPermission } from "~/lib/enums";
import { videoPermissionOptions } from "~/lib/enums";
import { fileChunkSize, multipartUploadSize } from "~/lib/file";
import type { VideoPart } from "~/lib/interface";
import { uploadFileWithProgress } from "~/lib/utils";
import type { GenerateMultipartUploadUrlsOutput } from "~/server/api/routers/aws";
import { api } from "~/trpc/react";
import { AthleteSelect } from "../AthleteSelect";
import { CompetitionSelect } from "../CompetitionSelect";
import { Loading } from "../Loading";
import { Metadata } from "../Metadata";
import { VideoDate } from "../VideoDate";
import type { MultiUploadFile } from "./MultiVideoUpload";

const threads = 4;

export const AccountVideoUploadItem = ({
  video,
  roleSports,
  onUpdate,
}: {
  video: MultiUploadFile;
  removeItem: () => void;
  onUpdate: (video: MultiUploadFile) => void;
  roleSports: Sport[];
}) => {
  const { file, sport, permission, title } = video;

  const controllerRef = useRef<AbortController | null>(null);

  const [multipartSignedUrls, setMultipartSignedUrls] =
    useState<GenerateMultipartUploadUrlsOutput>();
  const [partsUploaded, setPartsUploaded] = useState<VideoPart[]>([]);
  const [singleUploadProgress, setSingleUploadProgress] = useState(0);
  const [uploadedPartCount, setUploadedPartCount] = useState(0);

  const [uploadError, setUploadError] = useState(false);

  const partsTotal = multipartSignedUrls?.signedUrls.length ?? 1;
  const uploadProgress = singleUploadProgress
    ? singleUploadProgress
    : (uploadedPartCount / partsTotal) * 100;
  const isUploaded = uploadProgress === 100;

  const videoSport = video?.sport;

  const { mutateAsync: generateUrl } = api.aws.generateSignedUrl.useMutation();
  const { mutateAsync: generateMultipartUploadUrls } =
    api.aws.generateMultipartUploadUrls.useMutation({
      onSuccess: (data) => {
        setUploadError(false);
        setMultipartSignedUrls(data);
      },
      onError: () => {
        setUploadError(true);
        toast.error("Error initiating multipart upload");
      },
    });
  const { mutateAsync: completeMultipartUpload } =
    api.aws.completeMultipartUpload.useMutation({
      onSuccess: () => {
        setUploadError(false);
      },
      onError: () => {
        setUploadError(true);
        toast.error("Failed to upload video");
      },
    });
  const { mutateAsync: createMUXAsset } =
    api.video.createMUXAsset.useMutation();

  const {
    mutateAsync: upsert,
    isPending: isPendingUpdate,
    isSuccess,
  } = api.video.upsert.useMutation({
    onSuccess: ({ id, muxPlaybackId }) => {
      onUpdate({ ...video, id, sport, permission, title, muxPlaybackId });
      setUploadError(false);
    },
    onError: () => {
      toast.error("Failed to create video");
      setUploadError(true);
    },
  });

  useRunOnce(() => {
    const uploadVideo = async () => {
      try {
        //create video in db with minimal info
        const { id } = await upsert({
          title: file.name,
          sport,
          permission,
        });
        //upload video
        const muxAsset = await onUploadFile();
        if (!muxAsset) {
          throw new Error("Failed to upload video");
        }
        //create video in DB
        await upsert({
          id,
          ...muxAsset,
          muxPlaybackId: muxAsset.playbackId,
          newVideo: true,
        });
        setUploadError(false);
      } catch (error) {
        if (error instanceof Error) {
          toast.error(error.message);
        } else {
          toast.error("Failed to upload video");
        }
        setUploadError(true);
      }
    };

    if (file && sport && permission) {
      void uploadVideo();
    }
  });

  const onUploadFile = async () => {
    const filename = file.name;

    // if file size is less than 100MB, upload normally
    if (file.size <= multipartUploadSize) {
      const controller = new AbortController();
      controllerRef.current = controller;

      const { uploadUrl, fullFilename } = await generateUrl({
        filename,
      });

      await uploadFileWithProgress(
        uploadUrl,
        file,
        setSingleUploadProgress,
        controller.signal,
      );
      return await createMUXAsset({ fullFilename, filename });
    }
    return await onMultiUpload();
  };

  const onThreadUpload = async ({
    chunks,
    parts,
  }: {
    chunks: { chunk: Blob; url: string; partNumber: number }[];
    parts: VideoPart[];
  }) => {
    const promises = chunks.map(async ({ chunk, url, partNumber }) => {
      const uploadRes = await fetch(url, {
        method: "PUT",
        body: chunk,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (uploadRes.status !== 200) {
        throw new Error("Failed to upload part");
      }
      const ETag = uploadRes.headers.get("eTag");
      if (!ETag) {
        throw new Error("ETag not returned");
      }
      parts.push({
        ETag,
        PartNumber: partNumber,
      });
    });

    await Promise.all(promises);
  };

  const onMultiUpload = async () => {
    setUploadError(false);
    //always generate new signed urls,
    const { UploadId, fullFilename, signedUrls } =
      await generateMultipartUploadUrls({
        fullFilename: multipartSignedUrls?.fullFilename,
        filename: file.name,
        fileSize: file.size,
        uploadId: multipartSignedUrls?.UploadId,
      });

    const parts: VideoPart[] = [...partsUploaded];

    const currentThreads = [];

    //pickup the last part
    for (const { partNumber, signedUrl } of signedUrls.slice(
      partsUploaded.length,
    )) {
      const startPoint = (partNumber - 1) * fileChunkSize;
      const endPoint = partNumber * fileChunkSize;
      const chunk = file.slice(startPoint, endPoint);

      currentThreads.push({
        chunk,
        url: signedUrl,
        partNumber,
      });

      const isLastChunk = partNumber === signedUrls.length;

      if (currentThreads.length === threads || isLastChunk) {
        await onThreadUpload({
          chunks: currentThreads,
          parts,
        });
        currentThreads.length = 0;
      }

      setPartsUploaded(parts);
      setUploadedPartCount(parts.length);
    }

    parts.sort((a, b) => a.PartNumber - b.PartNumber);

    await completeMultipartUpload({
      uploadId: UploadId,
      fullFilename,
      parts,
    });

    return await createMUXAsset({ fullFilename, filename: file.name });
  };

  // const onCancel = () => {
  //   if (controllerRef.current) {
  //     controllerRef.current.abort();
  //     removeItem();
  //   }
  // };

  const onRetry = async () => {
    try {
      if (!navigator.onLine) {
        throw new Error(
          "You are currently offline. Please connect to the internet and try again.",
        );
      }
      const muxAsset = await onMultiUpload();
      await upsert({
        id: video.id,
        ...muxAsset,
        muxPlaybackId: muxAsset.playbackId,
        newVideo: true,
      });
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to upload video");
      }
      setUploadError(true);
    }
  };

  return (
    <div className="grid gap-2 lg:grid-cols-8">
      <div className="flex flex-col justify-between">
        <Label>DISPLAY TITLE</Label>
        <Input
          value={video.title}
          onChange={(value) => {
            onUpdate({
              ...video,
              title: value.target.value,
            });
          }}
        />
      </div>
      <SelectWithLabel
        label="SPORT"
        options={roleSports.map((x) => ({ label: x, value: x }))}
        value={video.sport}
        onValueChange={(updatedSport: string) => {
          if (!video) return;
          onUpdate({
            ...video,
            sport: updatedSport as Sport,
          });
        }}
      />
      <SelectWithLabel
        label="PERMISSION"
        options={videoPermissionOptions}
        value={video.permission}
        onValueChange={(updatedPermission) => {
          if (!video) return;
          onUpdate({
            ...video,
            permission: updatedPermission as VideoPermission,
          });
        }}
      />
      <CompetitionSelect
        label="COMPETITION"
        setSelectedCompetition={(value) => {
          onUpdate({
            ...video,
            competition: {
              ...value,
              type: value.type ?? null,
              location: value.location ?? "",
              isOfficial: value.isOfficial ?? false,
              date: value.date ?? "",
            },
          });
        }}
        selectedCompetition={video.competition}
        sport={videoSport}
      />
      <Metadata
        label="METADATA"
        selectedTags={video.metadata ?? []}
        // setSelectedTags={(value) => {
        //   onUpdate({
        //     ...video,
        //     metadata: value,
        //   });
        // }}
        onTagClick={(value) => {
          if (!value) return;
          if (video.metadata?.includes(value)) {
            onUpdate({
              ...video,
              metadata: video.metadata.filter((x) => x !== value),
            });
          } else {
            onUpdate({
              ...video,
              metadata: [...(video.metadata ?? []), value],
            });
          }
        }}
        sport={videoSport}
      />
      <AthleteSelect
        label="ATHLETES"
        selectedAthletes={video.athletes}
        setSelectedAthletes={(value) => {
          onUpdate({
            ...video,
            athletes: value,
          });
        }}
        sport={videoSport}
      />
      <div className="flex flex-col justify-between">
        <Label>VIDEO DATE</Label>
        <VideoDate
          selected={video.videoDate}
          onSelect={(videoDate) => {
            if (!videoDate) return;
            onUpdate({
              ...video,
              videoDate,
            });
          }}
        />
      </div>
      <div className="flex items-center gap-1">
        <p className="w-12">{uploadProgress.toFixed(2)}%</p>
        <div className="ml-4 flex items-center gap-x-1">
          {(isPendingUpdate || (uploadProgress < 100 && uploadProgress > 0)) &&
            !uploadError && <Loading />}
          {isUploaded && isSuccess && (
            <CircleCheck className="h-4 w-4 text-green" />
          )}
          {uploadError && (
            <div title="Error upload">
              <CircleAlert className="h-4 w-4 text-danger" />
            </div>
          )}
        </div>
        {/* {!isUploaded && (
          <Button variant="destructive" onClick={onCancel}>
            Remove
          </Button>
        )} */}
        {uploadError && (
          <Button onClick={() => void onRetry()} size="sm">
            Retry
          </Button>
        )}
        {isUploaded && (
          <Button
            disabled={!video.id}
            className="h-fit px-2 py-1"
            variant="default"
          >
            <Link href={`/account/videos/${video.id}`} target="__blank">
              Edit
            </Link>
          </Button>
        )}
      </div>
      {/* <div className="my-auto flex h-fit w-fit rounded-full border border-white px-2 py-0.5">
        <TriggerAIButton
          video={{
            id: video.id ?? "",
            status: video.status ?? VideoStatus.Raw,
            sport: video.sport,
            tags:
              video.metadata?.map((x) => ({
                videoId: video.id ?? "",
                text: x,
              })) ?? [],
          }}
        />
      </div> */}
    </div>
  );
};
