import { CompleteMultipartUploadCommand } from "@aws-sdk/client-s3";
import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import { env } from "~/env";
import { s3Client } from "~/server/api/routers/aws";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";

/**
 * @swagger
 * /api/v1/video/upload/complete:
 *   post:
 *     description: |
 *       To complete multipart upload. Call this endpoint after all parts are uploaded.
 *
 *       **Roles required:** api, admin / analyst
 *     tags: [Upload]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - filename
 *               - sport
 *               - permission
 *             properties:
 *               uploadId:
 *                 type: string
 *               fullFilename:
 *                 type: string
 *               parts:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     partNumber:
 *                       type: number
 *                     eTag:
 *                       type: string
 *     responses:
 *       200:
 *         description: A list of videos summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 uploadId:
 *                   type: string
 */

interface CompleteUpload {
  uploadId: string;
  fullFilename: string;
  parts: { partNumber: number; eTag: string }[];
}

export async function POST(request: NextRequest) {
  const { uploadId, fullFilename, parts } =
    (await request.json()) as CompleteUpload;

  try {
    await checkToken(request);

    const completeMultipartUploadCommand = new CompleteMultipartUploadCommand({
      Bucket: env.AWS_BUCKET_NAME,
      Key: fullFilename,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts.map((part) => ({
          PartNumber: +part.partNumber,
          ETag: part.eTag,
        })),
      },
    });

    await s3Client.send(completeMultipartUploadCommand);

    return Response.json({
      uploadId,
    });
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
