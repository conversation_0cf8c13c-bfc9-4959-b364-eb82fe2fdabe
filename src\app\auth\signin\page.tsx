import { getProviders } from "next-auth/react";
import { Logo } from "~/app/_components/Logo";
import {
  type ProviderProps,
  SignInButton,
} from "~/app/_components/SignInButton";

export default async function SignIn() {
  const providers = (await getProviders()) as Record<string, ProviderProps>;

  return (
    <div className=" top-0 z-[-2] h-full w-full bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.3),rgba(255,255,255,0))]">
      <div className="relative flex h-full flex-col items-center justify-center gap-4">
        <Logo />
        {Object.values(providers).map((provider) => (
          <div key={provider.id}>
            <SignInButton provider={provider} />
          </div>
        ))}
      </div>
    </div>
  );
}
