import { Button } from "~/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { cn } from "~/lib/utils";

export const ConfirmButton = ({
  label,
  description,
  open,
  disabled,
  danger,
  setOpen,
  onConfirm,
}: {
  label: string;
  description: string;
  open: boolean;
  disabled: boolean;
  danger?: boolean;
  setOpen: (open: boolean) => void;
  onConfirm: () => void;
}) => {
  return (
    <Popover open={open} onOpenChange={(open) => setOpen(open)}>
      <PopoverTrigger
        disabled={disabled}
        className={cn(
          "h-9 rounded-md border border-white bg-transparent px-3 text-sm hover:bg-accent disabled:opacity-50",
          danger
            ? "border-danger text-danger"
            : " hover:text-accent-foreground",
        )}
      >
        {label}
      </PopoverTrigger>
      <PopoverContent>
        <p>{description}</p>
        <div className="flex justify-end gap-3">
          <Button variant="outline" size="sm" onClick={() => setOpen(false)}>
            No
          </Button>
          <Button variant="outline" size="sm" onClick={onConfirm}>
            Yes
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
