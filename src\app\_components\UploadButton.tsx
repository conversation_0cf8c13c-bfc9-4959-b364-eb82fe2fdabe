"use client";

import { type ChangeEvent, useState, useRef } from "react";
import toast from "react-hot-toast";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Progress } from "~/components/ui/progress";
import { cn, uploadFileWithProgress } from "~/lib/utils";
import { api } from "~/trpc/react";
import { MUXPlayer } from "~/app/_components/MUXPlayer";
import type { MuxVideoFile, VideoPart } from "~/lib/interface";
import { VideoOff } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Loading } from "./Loading";
import { fileChunkSize, multipartUploadSize } from "~/lib/file";
import { LinkVideosModal } from "./LinkVideosModal";

interface VideoMetadata {
  name: string;
  type: string;
  size: number;
  duration: number;
  width: number;
  height: number;
}

export const UploadButton = ({
  label = "Video",
  mux,
  setMux,
  onSuccess,
  setVideoPauseTime,
  showFilename,
  videoId,
}: {
  label?: string;
  mux?: MuxVideoFile;
  setMux?: (mux: MuxVideoFile) => void;
  onSuccess?: () => void;
  setVideoPauseTime?: (time: number) => void;
  showFilename: boolean;
  videoId?: string;
}) => {
  const controllerRef = useRef<AbortController | null>(null);

  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [metadata, setMetadata] = useState<VideoMetadata | null>(null);

  // Get current video to check if it has a parentId
  const { data: currentVideo } = api.video.get.useQuery(
    { id: videoId ?? "" },
    { enabled: !!videoId },
  );

  // Check if the video is a child (has parentId)
  const isChild = !!currentVideo?.parentId;

  const { mutateAsync: generateUrl } = api.aws.generateSignedUrl.useMutation();
  const { mutateAsync: generateMultipartUploadUrls } =
    api.aws.generateMultipartUploadUrls.useMutation();
  const { mutateAsync: completeMultipartUpload } =
    api.aws.completeMultipartUpload.useMutation();

  const { mutateAsync: createMUXAsset } = api.video.createMUXAsset.useMutation({
    onSuccess: (asset) => {
      if (setMux) {
        setMux({
          ...asset,
          muxPlaybackId: asset.playbackId,
          duration: metadata?.duration ?? 0,
          maxHeight: metadata?.height ?? 0,
          maxWidth: metadata?.width ?? 0,
        });
      }
      onSuccess?.();
    },
  });

  const onFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    //check if file is not a video
    if (!file) return;
    if (!file.type.includes("video")) {
      toast.error("Please upload a video file");
      return;
    }

    await onUploadFile(file);
  };

  const onUploadFile = async (file: File) => {
    setIsUploading(true);
    const fileSize = file.size;

    const video = document.createElement("video");
    video.preload = "metadata";
    video.onloadedmetadata = async () => {
      const duration = video.duration;
      const width = video.videoWidth;
      const height = video.videoHeight;
      setMetadata({
        name: file.name,
        type: file.type,
        size: file.size,
        duration,
        width,
        height,
      });
      URL.revokeObjectURL(video.src);
    };
    video.src = URL.createObjectURL(file);

    // if file size is less than 100MB, upload normally
    if (fileSize <= multipartUploadSize) {
      await onNormalUpload(file);
      return;
    }
    await onMultiUpload(file);
  };

  const onNormalUpload = async (file: File) => {
    try {
      const { uploadUrl, fullFilename } = await generateUrl({
        filename: file.name,
      });
      const controller = new AbortController();
      controllerRef.current = controller;

      const response = await uploadFileWithProgress(
        uploadUrl,
        file,
        setUploadProgress,
        controller.signal,
      );
      await createMUXAsset({ fullFilename, filename: file.name });
      toast.success("Video uploaded");
      return response;
    } catch {
      toast.error("Failed to upload video");
    } finally {
      setIsUploading(false);
    }
  };

  const onMultiUpload = async (file: File) => {
    try {
      const { UploadId, fullFilename, signedUrls } =
        await generateMultipartUploadUrls({
          filename: file.name,
          fileSize: file.size,
        });

      const parts: VideoPart[] = [];

      for (const { partNumber, signedUrl } of signedUrls) {
        const chunk = file.slice(
          (partNumber - 1) * fileChunkSize,
          partNumber * fileChunkSize,
        );

        const uploadRes = await fetch(signedUrl, {
          method: "PUT",
          body: chunk,
          headers: {
            "Content-Type": file.type,
          },
        });

        if (uploadRes.status !== 200) {
          throw new Error("Failed to upload part");
        }
        const ETag = uploadRes.headers.get("eTag");

        if (!ETag) {
          throw new Error("ETag not returned");
        }

        parts.push({
          ETag,
          PartNumber: partNumber,
        });

        setUploadProgress((partNumber / signedUrls.length) * 100);
      }

      await completeMultipartUpload({
        uploadId: UploadId,
        fullFilename,
        parts,
      });

      await createMUXAsset({ fullFilename, filename: file.name });
      toast.success("Video uploaded");
    } catch (e) {
      toast.error("Failed to upload video");
      if (typeof e === "string") toast.error(e);
    } finally {
      setIsUploading(false);
    }
  };

  const onPause = (timestamp: number) => {
    if (setVideoPauseTime) {
      setVideoPauseTime(timestamp);
    }
  };

  const onCancelUpload = () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      toast.error("Upload cancelled");
    }
  };

  return (
    <div className="flex flex-col justify-center space-y-2">
      {!mux && (
        <div className="flex aspect-video w-full items-center justify-center rounded-3xl border border-dashed border-white">
          <div className="flex items-center justify-center rounded-md">
            <VideoOff className="h-10 w-10 text-white" />
          </div>
        </div>
      )}
      {mux && (
        <div className="flex flex-col gap-4">
          <MUXPlayer
            thumbnailToken={mux.thumbnailToken}
            playbackToken={mux.playbackToken}
            playbackId={mux.muxPlaybackId}
            storyboardToken={mux.storyboardToken}
            onPause={onPause}
          />
          {showFilename && (
            <p className="text-sm italic text-gray-300">{mux.filename}</p>
          )}
        </div>
      )}

      <div className="flex justify-between gap-4">
        <Label
          htmlFor="videoFile"
          className={cn(
            "button-base w-fit cursor-pointer rounded-full bg-orange px-4 py-2 text-white",
            isUploading && "cursor-not-allowed opacity-50",
          )}
        >
          {mux?.muxPlaybackId ? "Update Video" : label}
          {isUploading && <Loading className="ml-2" />}
        </Label>
        <LinkVideosModal disabled={isChild} />
        <Input
          id="videoFile"
          type="file"
          placeholder="Upload Video"
          accept="video/*"
          onChange={onFileChange}
          className="hidden"
          disabled={isUploading}
        />

        {isUploading && uploadProgress > 0 && (
          <Button onClick={onCancelUpload}>Cancel</Button>
        )}
      </div>
      {isUploading && uploadProgress > 0 && (
        <Progress value={uploadProgress} className="w-full" />
      )}
    </div>
  );
};
