import { createSwaggerSpec } from "next-swagger-doc";
import "server-only";

export const getApiDocs = async () => {
  const spec = createSwaggerSpec({
    apiFolder: "src/app/api",
    definition: {
      openapi: "3.0.0",
      info: {
        title: "Swagger API DOC",
        version: "1.0",
      },
      tags: [
        { name: "Get/list video(s)", description: "Get videos" },
        { name: "Update video", description: "Update video" },
        { name: "Upload", description: "Upload video" },
        { name: "Video playback", description: "Video playback" },
      ],
      components: {
        securitySchemes: {
          BearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
          },
        },
      },
      security: [],
    },
  });
  return spec;
};
