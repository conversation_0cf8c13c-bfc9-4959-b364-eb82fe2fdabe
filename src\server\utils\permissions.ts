import { TRPCError } from "@trpc/server";
import type { Context } from "../api/trpc";
import { type Sport, UserRole, VideoPermission } from "~/lib/enums";
import type { DecodedToken } from "~/lib/interface";
import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";
import jwksClient from "jwks-rsa";
import { env } from "~/env";
import { getUserSports } from "~/lib/roles";

const getRoles = (ctx: Context) => {
  if (!ctx.session?.user) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User is not authenticated",
    });
  }
  return ctx.session.user.roles;
};

export const checkAdmin = (ctx: Context) => {
  const roles = getRoles(ctx);
  if (!roles.includes(UserRole.admin)) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User does not have permission to access admin",
    });
  }
};

export const checkSport = (ctx: Context, sport: Sport) => {
  const roles = getRoles(ctx) as UserRole[];
  const userSports = getUserSports(roles);
  // if (roles.includes(UserRole.admin)) return;
  if (!userSports.includes(sport)) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User does not have permission to access this sport",
    });
  }
};

export const checkVideoPermission = async (
  ctx: Context,
  permission: VideoPermission,
  videoAthletesIds?: string[],
) => {
  const roles = getRoles(ctx);
  if (roles.includes(UserRole.admin)) return;
  if (permission === VideoPermission.public) return;
  if (!videoAthletesIds || videoAthletesIds.length < 1) return;

  let hasAthletePermission = false;

  for (const athleteId of videoAthletesIds) {
    if (ctx.session?.user.athletes?.includes(athleteId)) {
      hasAthletePermission = true;
      return;
    }
  }

  if (!hasAthletePermission) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User does not have permission to access this video",
    });
  }
};

const ptaClient = jwksClient({
  jwksUri: `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/protocol/openid-connect/certs`,
  requestHeaders: {
    "User-Agent": "custom-user-agent",
  },
});

/**
 * check token for external incoming requests
 */
export const checkToken = async (request: NextRequest, roles?: string[]) => {
  const method = request.method;
  const auth = request.headers.get("authorization");
  const token = auth?.replace("Bearer ", "");
  if (!token) {
    throw new TRPCError({
      message: "No token provided",
      code: "UNAUTHORIZED",
    });
  }

  const decodedToken = jwt.decode(token, { complete: true });

  if (!decodedToken)
    throw new TRPCError({ message: "Invalid token", code: "UNAUTHORIZED" });

  const tokenPayload = decodedToken?.payload as DecodedToken;

  const signingKeys = await ptaClient.getSigningKey(tokenPayload.kid);
  const publicKey = signingKeys.getPublicKey();
  jwt.verify(token, publicKey, {
    algorithms: ["RS256"],
  });

  if (method !== "GET") {
    if (
      !tokenPayload.roles.includes(UserRole.api) ||
      (!tokenPayload.roles.includes(UserRole.admin) &&
        !tokenPayload.roles.includes(UserRole.analyst))
    ) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User does not have permission",
      });
    }
  }

  checkPTAPermission(tokenPayload, roles);
  return { token, tokenPayload };
};

export const checkPTAPermission = (
  decodedToken: DecodedToken,
  roles?: string[],
) => {
  if (
    !decodedToken.aud.includes("videoplatform") ||
    !decodedToken.aud.includes("ptaplatform")
  ) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User does not have permission",
    });
  }
  if (roles) {
    if (roles.some((role) => !decodedToken.roles.includes(role as UserRole))) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User does not have permission",
      });
    }
  }
};
