import { Logo } from "./Logo";
import { UserButton } from "./UserButton";
import { Filters } from "./Filters";
import { MobileSidebar } from "./MobileNavbar";

export const Navbar = () => {
  return (
    <div className="flex items-center justify-between gap-2 bg-grey p-4 text-white">
      <div className="flex space-x-4">
        <MobileSidebar />

        <Logo />
      </div>

      {/* <form>
        <button
          formAction={async () => {
            "use server";
            await api.seed.videoDate();
          }}
        >
          seed
        </button>
      </form> */}
      <div className="flex flex-1 items-center justify-end space-x-4">
        <div className="hidden md:block">
          <Filters />
        </div>
        <div className="hidden items-center space-x-4 md:block">
          <UserButton />
        </div>
      </div>
    </div>
  );
};
