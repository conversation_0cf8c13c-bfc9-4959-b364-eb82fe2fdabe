export enum UserRole {
  admin = "admin",
  analyst = "analyst",
  support_staff = "support_staff",
  coach = "coach",
  athlete = "athlete",
  guest = "guest",
  pta_platform_access = "pta_platform_access",
  api = "api",
  api_modify = "api_modify",
  video_platform_access = "video_platform_access",
  sport_athletics = "sport_athletics",
  sport_cycling = "sport_cycling",
  sport_kayak = "sport_kayak",
  sport_rowing = "sport_rowing",
  sport_sailing = "sport_sailing",
  sport_snowsports = "sport_snowsports",
  sport_swimming = "sport_swimming",
  sport_training = "sport_training",
  sport_trampoline = "sport_trampoline",
  sport_beachvolleyball = "sport_beachvolleyball",
  sport_boxing = "sport_boxing",
  sport_climbing = "sport_climbing",
  sport_diving = "sport_diving",
  sport_equestrian = "sport_equestrian",
  sport_triathlon = "sport_triathlon",
  sport_canoeslalom = "sport_canoeslalom",
}

export const userRoles = Object.values(UserRole) as [UserRole, ...UserRole[]];

export enum Sport {
  athletics = "athletics",
  cycling = "cycling",
  canoeslalom = "canoe_slalom",
  kayak = "kayak",
  rowing = "rowing",
  sailing = "sailing",
  snowsports = "snow_sports",
  swimming = "swimming",
  training = "training",
  trampoline = "trampoline",
  beachvolleyball = "beach_volleyball",
  boxing = "boxing",
  climbing = "climbing",
  diving = "diving",
  equestrian = "equestrian",
  triathlon = "triathlon",
}

export const sports = Object.values(Sport) as [Sport, ...Sport[]];

export const sportOptions = sports.map((x) => ({
  label: x,
  value: x,
}));

export enum VideoStatus {
  Raw = "Raw",
  Trimmed = "Trimmed",
  Tagged_by_AI = "Tagged_by_AI",
  AI_in_Progress = "AI_in_Progress",
  AI_Failed = "AI_Failed",
  Tagged = "Tagged",
  Review = "Review",
  Analysed = "Analysed",
}

export enum FitMode {
  Preserve = "preserve",
  Stretch = "stretch",
  Crop = "crop",
  Smartcrop = "smartcrop",
  Pad = "pad",
}

export const fitModes = Object.values(FitMode) as [FitMode, ...FitMode[]];

export const videoStatuses = Object.values(VideoStatus) as [
  VideoStatus,
  ...VideoStatus[],
];

export enum VideoPermission {
  public = "public",
  restricted = "restricted",
}

export const videoPermissions = Object.values(VideoPermission) as [
  VideoPermission,
  ...VideoPermission[],
];

export const videoPermissionOptions = videoPermissions.map((x) => ({
  label: x,
  value: x,
}));

export const booleans = [
  {
    label: "Yes",
    value: true,
  },
  {
    label: "No",
    value: false,
  },
];

export enum SortBy {
  name = "name",
  video_date = "video_date",
}

export const sortBy = Object.values(SortBy) as [SortBy, ...SortBy[]];
