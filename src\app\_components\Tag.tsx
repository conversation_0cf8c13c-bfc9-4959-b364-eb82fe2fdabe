import { CircleX } from "lucide-react";

export const Tag = ({
  label,
  onClose,
}: {
  label: string;
  onClose?: () => void;
}) => {
  return (
    <div className="tag">
      <span className="text-orange">{label.replaceAll("_", " ")}</span>
      {onClose && (
        <CircleX
          onClick={onClose}
          className="h-4 w-4 shrink-0 cursor-pointer"
        />
      )}
    </div>
  );
};
