import dayjs from "dayjs";
import { and, eq, isNotNull, isNull, lt, or } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { db } from "~/server/db";
import { videos } from "~/server/db/schema";

export async function GET(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  //only protect this endpoint in prod
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return Response.json(
      {
        code: "UNAUTHORIZED",
      },
      {
        status: 401,
      },
    );
  }
  const oneDayBefore = dayjs().subtract(1, "day").toDate();

  // delete videos that are not published since 24 hours
  await db
    .update(videos)
    .set({ isDeleted: true })
    .where(
      and(
        //1: if updatedAt is null, then check createdAt
        or(
          and(isNull(videos.updatedAt), lt(videos.createdAt, oneDayBefore)),
          and(isNotNull(videos.updatedAt), lt(videos.updatedAt, oneDayBefore)),
        ),
        //2: filter draft videos only
        eq(videos.isDraft, true),
        //3: filter videos that do not have fullFilename (means it's not in s3) or muxAssetId (means is not in mux)
        or(isNull(videos.fullFilename), isNull(videos.muxAssetId)),
      ),
    );

  return Response.json({ success: true });
}
