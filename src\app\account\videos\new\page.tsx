import { TRPCError } from "@trpc/server";
import { BackButton } from "~/app/_components/BackButton";
import { ErrorBox } from "~/app/_components/ErrorBox";
import { AccountVideoEdit } from "~/app/_components/account/VideoEdit";

export default async function AddVideoPage() {
  try {
    return (
      <main>
        <BackButton />
        <div className="flex flex-col items-center justify-center xl:p-16">
          <AccountVideoEdit />
        </div>
      </main>
    );
  } catch (error) {
    if (error instanceof TRPCError) {
      return <ErrorBox code={error.code} message={error.message} />;
    } else {
      return <div>An unexpected error occurred</div>;
    }
  }
}
