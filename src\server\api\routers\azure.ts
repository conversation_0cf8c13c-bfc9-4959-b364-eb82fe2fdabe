import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { env } from "~/env";
import { Sport } from "~/lib/enums";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

export const azureRouter = createTRPCRouter({
  triggerAI: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        sport: z.enum([Sport.swimming, "shotput"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      //call tagger api endpoint to trigger AI processing
      const url = `${env.TAGGER_URL}/api/v1/${input.sport}/${input.id}/process`;
      const res = await fetch(url, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${ctx.session.accessToken}`,
          "Content-Type": "application/json",
        },
      });
      if (res.status !== 200) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to trigger AI processing",
        });
      }
    }),
});
