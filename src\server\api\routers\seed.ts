import { type SQL, sql, inArray } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { videos } from "~/server/db/schema";
import { db } from "~/server/db";

export const seedRouter = createTRPCRouter({
  videoDate: protectedProcedure.mutation(async ({ ctx }) => {
    const videoData = await ctx.db.query.videos.findMany();

    const ids: string[] = [];
    const sqlChunks: SQL[] = [sql`(case`];

    videoData.forEach((video) => {
      if (!video.videoDate) {
        ids.push(video.id);
        sqlChunks.push(
          sql`when ${videos.id} = ${video.id} then ${video.createdAt}`,
        );
      }
    });

    sqlChunks.push(sql`end)`);
    const finalSql = sql.join(sqlChunks, sql.raw(" "));

    await db
      .update(videos)
      .set({
        videoDate: finalSql,
      })
      .where(inArray(videos.id, ids));
  }),
});
