import { createId } from "@paralleldrive/cuid2";
import { TRPCError } from "@trpc/server";
import dayjs from "dayjs";
import type { NextRequest } from "next/server";
import { VideoPermission, type Sport } from "~/lib/enums";
import { getUserSports } from "~/lib/roles";
import { db } from "~/server/db";
import { accounts, users, videos } from "~/server/db/schema";
import {
  generateMultiuploadUrls,
  generateSignedUrlForUpload,
} from "~/server/utils/aws";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";

/**
 * @swagger
 * /api/v1/video/upload:
 *   post:
 *     description: |
 *       Get upload url(s) to upload a video. When chunkSize and fileSize are not passed, or chunkSize is larger than file size, it will do normal upload and return an upload url, uploadUrls will be empty. When passing chunkSize and fileSize, it will do multipart upload and return an uploadId and uploadUrls will contain multiple signed urls. You will need this uploadId and ETag, part number returned from part uploads to complete the whole upload. You can get ETag from response headers.
 *
 *       **Roles required:** api, admin / analyst
 *     tags: [Upload]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - filename
 *               - sport
 *               - permission
 *             properties:
 *               title:
 *                 type: string
 *               filename:
 *                 type: string
 *               sport:
 *                 type: string
 *               permission:
 *                 type: string
 *                 enum: [public, restricted]
 *               chunkSize:
 *                 type: number
 *               fileSize:
 *                 type: number
 *     responses:
 *       200:
 *         description: A list of videos summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 videoId:
 *                   type: string
 *                 uploadUrl:
 *                   type: string
 *                   format: uri
 *                 uploadId:
 *                   type: string
 *                 fullFilename:
 *                   type: string
 *                 uploadUrls:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       partNumber:
 *                        type: number
 *                       signedUrl:
 *                        type: string
 *                 expiry:
 *                   type: string
 */

interface Upload {
  title: string;
  filename: string;
  sport: Sport;
  permission: VideoPermission;
  chunkSize?: number;
  fileSize?: number;
}

export async function POST(request: NextRequest) {
  const {
    title,
    filename,
    permission,
    sport,
    chunkSize: chunkSizeBody,
    fileSize: fileSizeBody,
  } = (await request.json()) as Upload;

  const chunkSize = chunkSizeBody ? +chunkSizeBody : 0;
  const fileSize = fileSizeBody ? +fileSizeBody : 0;

  try {
    const { tokenPayload } = await checkToken(request);
    const { sub, name, email, preferred_username, roles } = tokenPayload;
    let userId: string;

    const userSports = getUserSports(roles);
    // const isAdmin = roles.includes(UserRole.admin);
    const isAdmin = false; //admin needs sports roles as well now
    if (!isAdmin && !userSports.includes(sport)) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User does not have permission",
      });
    }

    return await db.transaction(async (trx) => {
      const existingAccount = await trx.query.accounts.findFirst({
        where: (account, { eq }) => eq(account.providerAccountId, sub),
      });
      if (existingAccount) {
        userId = existingAccount.userId;
      } else {
        const newUserId = createId();
        await trx.insert(users).values({
          id: newUserId,
          name: name ?? preferred_username ?? "",
          email: email ?? "",
        });
        await trx.insert(accounts).values({
          userId: newUserId,
          type: "oauth",
          provider: "keycloak",
          providerAccountId: sub,
        });

        userId = newUserId;
      }

      let fullFilename = "";
      let expiresIn = 0;
      let uploadUrl = "";
      let uploadUrls: { partNumber: number; signedUrl: string }[] = [];
      let uploadId = "";

      const isMultipart =
        chunkSize && fileSize && chunkSize > 0 && chunkSize < fileSize;

      if (!isMultipart) {
        const res = await generateSignedUrlForUpload({
          filename,
        });
        fullFilename = res.fullFilename;
        uploadUrl = res.uploadUrl;
        expiresIn = res.expiresIn;
      }
      if (isMultipart) {
        const res = await generateMultiuploadUrls({
          filename,
          fileSize,
          chunkSize,
        });
        fullFilename = res.fullFilename;
        uploadUrls = res.signedUrls;
        expiresIn = res.expiresIn;
        uploadId = res.UploadId;
      }

      const id = createId();

      await trx.insert(videos).values({
        id,
        createdById: userId,
        title,
        sport,
        filename,
        fullFilename,
        permission: permission ?? VideoPermission.restricted,
      });

      return Response.json({
        videoId: id,
        uploadUrl,
        uploadUrls,
        uploadId,
        fullFilename,
        expiry: dayjs().add(expiresIn, "seconds").toISOString(),
      });
    });
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
