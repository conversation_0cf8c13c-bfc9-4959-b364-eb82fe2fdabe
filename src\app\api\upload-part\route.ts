import { UploadPartCommand } from "@aws-sdk/client-s3";
import { NextResponse, type NextRequest } from "next/server";
import { s3Client } from "~/server/api/routers/aws";

/**
 *
 * @deprecated use `generateMultipartUploadUrls` instead. This can't work with serverless functions because size limit.
 */
export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const PartNumber = +(formData.get("partNumber") as string);
  const uploadId = formData.get("uploadId") as string;
  const Key = formData.get("key") as string;
  const chunk = formData.get("chunk") as File;

  const file = await chunk.arrayBuffer();

  try {
    const command = new UploadPartCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key,
      PartNumber: PartNumber,
      UploadId: uploadId,
      Body: file as Buffer,
    });

    const response = await s3Client.send(command);

    return NextResponse.json({ ETag: response.ETag, PartNumber });
  } catch (error) {
    console.error(`Error uploading part ${PartNumber}:`, error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 500 },
    );
  }
}
