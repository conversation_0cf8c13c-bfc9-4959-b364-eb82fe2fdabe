"use client";

import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { Sport, VideoStatus } from "~/lib/enums";
import { cn } from "~/lib/utils";
import type { VideoTag } from "~/server/db/schema";
import { api } from "~/trpc/react";

interface Props {
  video: {
    id: string;
    status: VideoStatus | null;
    sport: Sport | null;
    tags: VideoTag[];
  };
  className?: string;
}

export const TriggerAIButton = ({ video, className }: Props) => {
  const router = useRouter();
  const id = video.id;
  let sport: Sport | "shotput" | null = video.sport;

  const checkAIReady = () => {
    if (video.status !== VideoStatus.Raw || !video.sport) return false;
    if (video.sport === Sport.swimming) return true;
    if (video.sport === Sport.athletics) {
      if (video.tags.find((x) => x.text.toLowerCase() === "shotput")) {
        sport = "shotput";
        return true;
      }
    }
    return false;
  };

  const isAIReady = checkAIReady();

  const { mutateAsync: triggerAI, isPending } = api.azure.triggerAI.useMutation(
    {
      onSuccess: () => {
        router.refresh();
      },
    },
  );

  const onClick = () => {
    if (!sport) return;
    if (sport === "shotput" || sport === Sport.swimming) {
      void toast.promise(triggerAI({ id, sport }), {
        loading: "Triggering AI...",
        success: "AI processing triggered",
        error: "Failed to trigger AI processing",
      });
    }
  };

  if (!isAIReady) return null;

  return (
    <button
      onClick={onClick}
      disabled={isPending}
      className={cn(" disabled:cursor-not-allowed", className)}
    >
      Trigger AI
    </button>
  );
};
