"use client";

import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "~/components/ui/button";

export const BackButton = () => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <Button
      className="my-2 flex w-fit items-center"
      variant="ghost"
      onClick={handleBack}
    >
      <ChevronLeft className="h-4 w-4" />
      <p>Back</p>
    </Button>
  );
};
