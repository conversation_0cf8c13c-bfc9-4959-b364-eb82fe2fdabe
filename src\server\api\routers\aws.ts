import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import {
  S3Client,
  CompleteMultipartUploadCommand,
  UploadPartCommand,
  CreateMultipartUploadCommand,
} from "@aws-sdk/client-s3";
import { env } from "~/env";
import {
  generateMultiuploadUrls,
  generateSignedUrlForUpload,
} from "~/server/utils/aws";
import dayjs from "dayjs";
import { TRPCError } from "@trpc/server";
import type { RouterOutputs } from "../root";

type Outputs = RouterOutputs["aws"];
export type GenerateMultipartUploadUrlsOutput =
  Outputs["generateMultipartUploadUrls"];

export const s3Client = new S3Client({
  region: "ap-southeast-2",
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
  useAccelerateEndpoint: true,
});

export const awsRouter = createTRPCRouter({
  generateSignedUrl: protectedProcedure
    .input(z.object({ filename: z.string() }))
    .mutation(async ({ input }) => {
      const { filename } = input;
      return generateSignedUrlForUpload({ filename });
    }),

  generateMultipartUploadUrls: protectedProcedure
    .input(
      z.object({
        fullFilename: z.string().optional(),
        filename: z.string(),
        fileSize: z.number(),
        uploadId: z.string().optional(),
      }),
    )
    .mutation(({ input }) => {
      return generateMultiuploadUrls(input);
    }),

  /**
   * @deprecated use `generateMultipartUploadUrls` instead
   */
  initMultipartUpload: protectedProcedure
    .input(z.object({ filename: z.string() }))
    .mutation(async ({ input }) => {
      const { filename } = input;
      const date = dayjs().format("YYYY/MM");
      const time = Date.now();
      const fullFilename = `${date}/${time}_${filename}`;

      const createMultipartUploadCommand = new CreateMultipartUploadCommand({
        Bucket: env.AWS_BUCKET_NAME,
        Key: fullFilename,
      });
      const { UploadId } = await s3Client.send(createMultipartUploadCommand);

      if (!UploadId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create multipart upload",
        });
      }

      return { UploadId, fullFilename };
    }),
  uploadPart: protectedProcedure
    .input(
      z.object({
        UploadId: z.string(),
        fullFilename: z.string(),
        partNumber: z.number(),
      }),
    )
    .mutation(async ({ input }) => {
      const { UploadId, fullFilename, partNumber } = input;
      const uploadPartCommand = new UploadPartCommand({
        Bucket: env.AWS_BUCKET_NAME,
        Key: fullFilename,
        UploadId,
        PartNumber: partNumber,
      });
      const response = await s3Client.send(uploadPartCommand);

      return {
        partNumber,
        ETag: response.ETag,
      };
    }),
  completeMultipartUpload: protectedProcedure
    .input(
      z.object({
        uploadId: z.string(),
        fullFilename: z.string(),
        parts: z.array(
          z.object({
            PartNumber: z.number(),
            ETag: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      const { uploadId, fullFilename, parts } = input;

      const completeMultipartUploadCommand = new CompleteMultipartUploadCommand(
        {
          Bucket: env.AWS_BUCKET_NAME,
          Key: fullFilename,
          UploadId: uploadId,
          MultipartUpload: {
            Parts: parts,
          },
        },
      );

      await s3Client.send(completeMultipartUploadCommand);
    }),
});
