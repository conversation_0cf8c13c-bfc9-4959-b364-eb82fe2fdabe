import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Filter as FilterIcon } from "lucide-react";
import { cn } from "~/lib/utils";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

export interface Option<T> {
  label: string;
  value: T;
}

export const TableFilter = <T extends string>({
  paramKey,
  options,
}: {
  paramKey: string;
  options: Option<T>[];
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const filterValues = searchParams.getAll(paramKey);

  const onOptionClick = (option: Option<T>) => {
    const params = new URLSearchParams(searchParams.toString());

    const oldValues = params.getAll(paramKey);
    const value = option.value.toString();
    let newValues: string[] = [];

    if (oldValues.includes(value)) {
      newValues = oldValues.filter((v) => v !== value);
    } else {
      newValues = [...oldValues, value];
    }
    params.delete(paramKey);
    params.delete("page");
    newValues.forEach((v) => {
      params.append(paramKey, v);
    });

    router.push(`${pathname}?${params.toString()}`);
  };

  const isActive = filterValues.length > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <FilterIcon className={cn("h-4 w-4", isActive && "text-green")} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {options.map((option) => (
          <DropdownMenuCheckboxItem
            key={option.label}
            checked={filterValues.includes(option.value.toString())}
            onClick={() => {
              onOptionClick(option);
            }}
          >
            {option.label}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
