import { env } from "~/env";

export const getServiceToken = async () => {
  const tokenUrl = `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/protocol/openid-connect/token`;
  const tokenRes = await fetch(tokenUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      grant_type: "client_credentials",
      client_id: "cub-videoplatform-api",
      client_secret: env.KEYCLOAK_VIDEO_PLATFORM_API_CLIENT_SECRET,
      scope: "pta-platform-service",
    }),
  });
  const tokenJson = (await tokenRes.json()) as { access_token: string };
  return tokenJson;
};
