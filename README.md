# Introduction:

This code repository contains the HPSNZ Web Video application - a comprehensive platform for managing and viewing sports videos.
The application allows users to upload, edit, and view videos based on their assigned roles and permissions.
It provides features such as video uploading, athlete and competition association, and search functionality.
The HPSNZ Web Video application provides

# Video Playform Overview:

Built with modern web application architecture using Next.js, React, tRPC, and Drizzle ORM. The application is built with TypeScript, providing type safety and improved developer experience. 
The frontend utilizes React components and hooks for building the user interface, while the backend leverages tRPC for efficient client-server communication. The database operations are handled using Drizzle ORM with a MySQL database.

---------------------------------------------------------------------------------------------------------------------------

# Development Environment Setup:
1. Install Node.js (version 16 or higher) and pnpm package manager.
2. Clone the repository: `git clone https://github.com/SparcGoldmine/VideoPlatform.git`
3. Navigate to the project directory: `cd hpsnz-web-video`
4. Install dependencies: `pnpm install`
5. Set up environment variables in a `.env` file based on the provided `.env.example`.
6. Configure your IDE with recommended extensions and settings for TypeScript, React, and Next.js development.

# Installation and Setup:
1. Start the development server: `pnpm dev`
2. Open the application in your browser at `http://localhost:3000`.

# Getting Started:
1. Sign in to the application using your Keycloak account.
2. Explore the available features based on your assigned user role.
3. Upload videos, associate them with athletes and competitions, and add tags.
4. Use the search functionality to find specific videos based on title, sport, athlete, or competition.
5. View and manage videos in the "Manage Videos" section.
6. Admins can access additional features like managing users and permissions.

---------------------------------------------------------------------------------------------------------------------------

# Key Points of Complexity:
1. Role-based Access Control:
   - The application implements role-based access control to restrict access to certain features and videos based on user roles.
   - User roles are defined in the `UserRole` enum and include admin, analyst, support staff, coach, athlete, and guest.
   - The `checkAdmin`, `checkSport`, and `checkVideoPermission` functions in `src/server/utils/permissions.ts` handle the authorization checks.

2. Video Upload and Processing:
   - Videos are uploaded to an AWS S3 bucket, and the upload process is managed using pre-signed URLs.
   - The uploaded videos are processed using the Mux video platform for transcoding, thumbnail generation, and playback.
   - The `src/server/utils/mux.ts` file contains utility functions for generating Mux tokens and thumbnails.

3. Database Schema and ORM:
   - The database schema is defined using Drizzle ORM in `src/server/db/schema.ts`.
   - The schema includes tables for users, videos, video athletes, video tags, and competitions.
   - Drizzle ORM provides type-safe database operations and migrations.

4. tRPC API:
   - The application uses tRPC for efficient client-server communication.
   - The tRPC routers and procedures are defined in `src/server/api/routers`.
   - The `src/trpc` directory contains the tRPC setup and configuration for both the client and server.

5. Integration with External Services:
   - The application integrates with the PTA (Player Tracking Analytics) platform to fetch athlete and competition data.
   - The `src/server/utils/pta.ts` file contains functions for making requests to the PTA API.

---------------------------------------------------------------------------------------------------------------------------

# Code Repository Structure:
- `src/app`: Contains the Next.js app directory structure, including pages, components, and layouts.
- `src/components`: Reusable UI components used throughout the application.
- `src/hooks`: Custom React hooks for common functionalities.
- `src/lib`: Utility functions, enums, and type definitions.
- `src/server`: Backend code, including API routes, database schema, and utility functions.
- `src/trpc`: tRPC setup and configuration for client and server.
- `public`: Static assets such as images and favicon.

---------------------------------------------------------------------------------------------------------------------------


# Deployments

### Vercel Deployment

Vercel integrates with GitHub to streamline your deployment workflow. The main branch is deployed to production by default, while other branches automatically get unique preview deployments for testing. 

You can link specific branches to custom domains if needed. 

Manage and monitor your deployments via the HPSNZ [Vercel](https://vercel.com/hpsnz) dashboard, ensuring efficient and reliable rollouts.

# Example Workflow
### _Feature Development:_

1. Create a new branch for your feature, e.g., feature/new-component.
2. Push your changes to GitHub. Vercel automatically creates a preview deployment for this branch.

### _Testing:_

Review and test your changes using the preview deployment URL provided by Vercel.
Share the URL with your team for further testing and feedback.


### _Merging and Deploying:_
Once the feature is ready, open a pull request and merge it into the main branch.
Vercel automatically deploys the updated main branch to production.
