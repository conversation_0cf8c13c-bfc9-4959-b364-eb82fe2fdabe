import { api } from "~/trpc/server";
import { VideoList } from "~/app/_components/VideoList";
import { getServerAuthSession } from "~/server/auth";
import { TRPCError } from "@trpc/server";
import { ErrorBox } from "~/app/_components/ErrorBox";
import type { VideoListSearchParams } from "~/lib/interface";
import { PaginationList } from "../_components/Pagination";
import { FilterTags } from "../_components/FilterTags";

//force update if there are data changes in db
export const dynamic = "force-dynamic";

export default async function VideoPage({
  searchParams,
}: {
  searchParams: VideoListSearchParams;
}) {
  const session = await getServerAuthSession();
  if (!session) {
    return;
  }

  const page = +(searchParams.page ?? 1);
  const pageSize = +(searchParams.pageSize ?? 12);
  const sports =
    typeof searchParams.sports === "string"
      ? [searchParams.sports]
      : searchParams.sports;

  const status =
    typeof searchParams.status === "string"
      ? [searchParams.status]
      : searchParams.status;

  const metadata =
    typeof searchParams.metadata === "string"
      ? [searchParams.metadata]
      : searchParams.metadata;

  const sortBy = searchParams.sort_by;

  const hasFilter = !!sports || !!status;

  try {
    const { videoList, videosCount } = await api.video.all({
      page: page - 1,
      pageSize,
      searchText: searchParams.searchText,
      sports: sports,
      videoStatuses: status,
      metadata,
      sortBy,
    });

    return (
      <main className="flex flex-col gap-3 p-6">
        {hasFilter && <FilterTags />}
        <VideoList videos={videoList} />
        <PaginationList
          itemsCount={videosCount ?? 0}
          searchParams={searchParams}
        />
      </main>
    );
  } catch (error) {
    if (error instanceof TRPCError) {
      return <ErrorBox code={error.code} message={error.message} />;
    } else {
      return <div>An unexpected error occurred</div>;
    }
  }
}
