import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import { checkToken } from "~/server/utils/permissions";
import { db } from "~/server/db";
import { eq } from "drizzle-orm";
import {
  UserRole,
  VideoPermission,
  type Sport,
  type VideoStatus,
} from "~/lib/enums";
import { videos } from "~/server/db/schema";
import Mux from "@mux/mux-node";
import { env } from "~/env";
import { getUserSports } from "~/lib/roles";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";
import {
  getAthletes,
  getAthletesAttr,
  getHpAthleteCached,
} from "~/server/utils/pta";

/**
 * @swagger
 * /api/v1/video/{id}:
 *   get:
 *     description: Get a video summary.
 *     tags: [Get/list video(s)]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *        200:
 *          description: Summary of a video
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                 id:
 *                   type: string
 *                 title:
 *                   type: string
 *                 thumbnail:
 *                   type: string
 *                 sport:
 *                   type: string
 *                   enum: [cycling, kayak, rowing, sailing, snow_sports, canoe_slalom, swimming, training, trampoline]
 *                 permission:
 *                   type: string
 *                   enum: [public, restricted]
 *                 isDraft:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 fps:
 *                   type: number
 *                 snowSportsRaceId:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [Raw, Trimmed, Tagged_by_AI, AI_in_Progress, AI_Failed, Tagged, Review, Analysed]
 *                 competitionId:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                 updatedAt:
 *                   type: string
 *   put:
 *     description: |
 *       Update a video.
 *
 *       **Roles required:** api, api_modify, admin / analyst
 *     tags: [Update video]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               filename:
 *                 type: string
 *               title:
 *                 type: string
 *               thumbnailTime:
 *                 type: number
 *               sport:
 *                 type: string
 *                 enum: [cycling, kayak, rowing, sailing, snow_sports, canoe_slalom, swimming, training, trampoline]
 *               permission:
 *                 type: string
 *                 enum: [public, restricted]
 *               isDraft:
 *                 type: boolean
 *               isDeleted:
 *                 type: boolean
 *               status:
 *                 type: string
 *                 enum: [Raw, Trimmed, Tagged_by_AI, AI_in_Progress, AI_Failed, Tagged, Review, Analysed]
 *               competitionId:
 *                 type: string
 *               fps:
 *                 type: number
 *               videoDate:
 *                 type: string
 *     responses:
 *       200:
 *         description: Video summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 title:
 *                   type: string
 *                 thumbnail:
 *                   type: string
 *                 sport:
 *                   type: string
 *                 permission:
 *                   type: string
 *                 path:
 *                   type: string
 *                 isDraft:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 status:
 *                   type: string
 *                 fps:
 *                   type: number
 *                 competitionId:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                 updatedAt:
 *                   type: string
 */

interface UpdateVideo {
  filename?: string;
  title?: string;
  thumbnailTime?: number;
  sport?: Sport;
  permission?: VideoPermission;
  isDraft?: boolean;
  isDeleted?: boolean;
  status?: VideoStatus;
  competitionId?: string;
  fps?: number | null;
  snowSportsRaceId?: string;
  videoDate?: string;
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const body = (await request.json()) as UpdateVideo;
  const id = params.id;

  try {
    const { tokenPayload, token } = await checkToken(request, [
      UserRole.api_modify,
    ]);
    await getVideoByIdForPermissionCheck(id, tokenPayload.roles, token);

    const video = await db.query.videos.findFirst({
      where: (fields) => eq(fields.id, id),
      columns: {
        muxPlaybackId: true,
      },
    });

    if (!video) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    const thumbnail = `https://image.mux.com/${video.muxPlaybackId}/thumbnail.png?time=${body.thumbnailTime}`;

    let videoDate = undefined;
    if (body.videoDate && body.videoDate.length > 0) {
      videoDate = new Date(body.videoDate);
    }

    await db
      .update(videos)
      .set({
        filename: body.filename,
        title: body.title,
        thumbnail,
        sport: body.sport,
        permission: body.permission,
        isDraft: body.isDraft,
        isDeleted: body.isDeleted,
        status: body.status,
        competitionId: body.competitionId,
        snowSportsRaceId: body.snowSportsRaceId,
        fps: body.fps,
        videoDate,
      })
      .where(eq(videos.id, id));

    const updatedVideo = await db.query.videos.findFirst({
      where: (fields) => eq(fields.id, id),
      columns: {
        id: true,
        title: true,
        thumbnail: true,
        sport: true,
        permission: true,
        isDraft: true,
        isDeleted: true,
        status: true,
        fps: true,
        competitionId: true,
        snowSportsRaceId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json(updatedVideo);
  } catch (error) {
    if (error instanceof TRPCError) {
      return Response.json(error, {
        status: getHTTPStatusCodeFromError(error.code),
      });
    }
    return Response.json(error, {
      status: 500,
    });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const videoId = params.id;

  try {
    const { tokenPayload, token } = await checkToken(request);
    // const isAdmin = tokenPayload.roles.includes(UserRole.admin);
    const userSports = getUserSports(tokenPayload.roles);

    if (!videoId) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Invalid video id",
      });
    }

    const video = await db.query.videos.findFirst({
      where: (fields) => eq(fields.id, videoId),
      columns: {
        id: true,
        title: true,
        sport: true,
        permission: true,
        muxAssetId: true,
        fullFilename: true,
        status: true,
        duration: true,
        snowSportsRaceId: true,
        isDraft: true,
        isDeleted: true,
        fps: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        athletes: {
          columns: {
            athleteId: true,
            videoId: true,
            // isHp: true,
          },
        },
        tags: {
          columns: {
            text: true,
          },
        },
        competition: {
          columns: {
            id: true,
          },
        },
      },
    });

    if (!video?.sport) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    const userHasSport = userSports.includes(video.sport);

    const userAthletes = await getAthletes({
      token,
      is_hp: true,
    });

    let userHasAthletePerm = false;
    if (video.permission === VideoPermission.public) {
      userHasAthletePerm = true;
    } else {
      const hpAthletes = await getHpAthleteCached();
      const hpAthleteIds = hpAthletes.map((x) => x.athlete_id);

      if (video.athletes.some((x) => !hpAthleteIds.includes(x.athleteId))) {
        userHasAthletePerm = true;
      } else if (userAthletes) {
        userHasAthletePerm = video.athletes.some((x) =>
          userAthletes.find((y) => y.athlete_id === x.athleteId),
        );
      }
    }

    if (!userHasSport || !userHasAthletePerm) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User does not have permission",
      });
    }

    if (video && video.duration === 0 && video.muxAssetId) {
      const mux = new Mux({
        tokenId: env.MUX_ACCESS_TOKEN_ID,
        tokenSecret: env.MUX_SECRET_KEY,
      });
      const asset = await mux.video.assets.retrieve(video.muxAssetId);
      video.duration = asset.duration ?? 0;

      const videoTrack = asset.tracks?.find((x) => x.type === "video");
      let maxHeight: number | undefined = 0;
      let maxWidth: number | undefined = 0;
      if (videoTrack) {
        maxHeight = videoTrack.max_height;
        maxWidth = videoTrack.max_width;
      }
      await db
        .update(videos)
        .set({
          duration: asset.duration,
          maxHeight,
          maxWidth,
        })
        .where(eq(videos.id, videoId));
    }

    const athletesWithAttr = await getAthletesAttr(video.athletes);
    const videoAthletes = video.athletes.map((athlete) => {
      const athleteWithAttr = athletesWithAttr.find(
        (x) => x.athleteId === athlete.athleteId,
      );
      return {
        ...athlete,
        name: athleteWithAttr?.name ?? "",
        isHp: athleteWithAttr?.isHp ?? false,
      };
    });

    return Response.json({
      ...video,
      path: video?.fullFilename,
      fullFilename: undefined,
      muxAssetId: undefined,
      athletes: videoAthletes,
    });
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
