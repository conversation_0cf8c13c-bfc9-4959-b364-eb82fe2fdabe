"use client";

import { signOut } from "next-auth/react";
import { Button } from "~/components/ui/button";
export interface ProviderProps {
  id: string;
  name: string;
  type: "oauth";
  signinUrl: string;
  callbackUrl: string;
}
export const SignoutButton = () => {
  return (
    <Button
      variant="ghost"
      onClick={() => signOut()}
      className="cursor-pointer rounded-full bg-orange md:bg-transparent"
    >
      Sign Out
    </Button>
  );
};
