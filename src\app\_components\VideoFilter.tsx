import { ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useVideoFilter } from "~/hooks/useVideoFilter";
import { Metadata } from "./Metadata";
import type { Sport } from "~/lib/enums";

interface FilterProps {
  filterKey: "sports" | "status" | "metadata" | "sort_by";
  filterEnumValues: string[];
  defaultValue?: string[];
}

const multiSelectKeys = ["status", "metadata"];

//filterKey = "sports", "status" etc
//filterProps = enum values for the filterKey eg videoStatuses etc
export const VideoFilter = ({
  filterKey,
  filterEnumValues,
  defaultValue,
}: FilterProps) => {
  const { filterValues, onFilterClick, searchParams } = useVideoFilter({
    filterKey,
    multiSelect: multiSelectKeys.includes(filterKey),
  });

  const selectedSport = searchParams.get("sports") ?? "";

  if (filterKey === "metadata") {
    return (
      <Metadata
        sport={selectedSport as Sport}
        selectedTags={filterValues}
        onTagClick={onFilterClick}
        Trigger={
          <Button
            size="default"
            variant="outline"
            className="mx-auto mt-2 border border-white capitalize md:mt-0"
          >
            Metadata
            <ChevronsUpDown className="ml-2 h-4 w-4" />
          </Button>
        }
      />
    );
  }

  const onItemClick = (filterValue: string) => {
    switch (filterKey) {
      case "sort_by":
        const isSelected =
          filterValues.includes(filterValue) ||
          (filterValues.length === 0 &&
            defaultValue &&
            defaultValue[0] === filterValue);
        if (!isSelected) {
          onFilterClick(filterValue);
        } else {
          onFilterClick(
            filterValue === "video_date" ? "video_date_asc" : "name_desc",
          );
        }
        break;
      default:
        onFilterClick(filterValue);
        break;
    }
  };

  const getItemChecked = (filterValue: string) => {
    switch (filterKey) {
      case "sort_by":
        //if no filters are selected and default value is present
        if (
          filterValues.length === 0 &&
          defaultValue &&
          defaultValue.includes(filterValue)
        ) {
          return true;
        }
        if (filterValue === "name") {
          return (
            filterValues.includes("name_desc") || filterValues.includes("name")
          );
        }
        if (filterValue === "video_date") {
          return (
            filterValues.includes("video_date") ||
            filterValues.includes("video_date_asc")
          );
        }
        return false;
      default:
        return filterValues.includes(filterValue);
    }
  };

  const getCheckIcon = () => {
    if (filterKey !== "sort_by") return null;
    if (
      filterValues.includes("video_date") ||
      filterValues.includes("name") ||
      filterValues.length === 0
    ) {
      return <ArrowDown className="h-4 w-4" />;
    }
    return <ArrowUp className="h-4 w-4" />;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="default"
          variant="outline"
          className="mx-auto mt-2 border border-white capitalize md:mt-0"
        >
          {filterKey.replaceAll("_", " ")}
          <ChevronsUpDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {filterEnumValues.map((filterValue) => (
          <DropdownMenuCheckboxItem
            key={filterValue}
            className="capitalize"
            checked={getItemChecked(filterValue)}
            onClick={() => {
              onItemClick(filterValue);
            }}
            checkIcon={getCheckIcon()}
          >
            {filterValue.replaceAll("_", " ")}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
