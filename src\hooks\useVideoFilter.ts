import { usePathname, useRouter, useSearchParams } from "next/navigation";

export function useVideoFilter({
  filterKey,
  multiSelect,
}: {
  filterKey: string;
  multiSelect?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());

  const filterValues = searchParams.getAll(filterKey);

  const onFilterClick = (filterValue: string) => {
    let newValues: string[] = [];
    const oldValues = params.getAll(filterKey);

    if (oldValues.includes(filterValue)) {
      newValues = oldValues.filter((v) => v !== filterValue);
    } else {
      if (multiSelect) {
        newValues = [...oldValues, filterValue];
      } else newValues = [filterValue];
    }

    params.delete(filterKey);
    params.delete("page");
    if (filterKey === "sports") {
      params.delete("metadata");
    }

    newValues.forEach((v) => {
      params.append(filterKey, v);
    });

    router.push(`${pathname}?${params.toString()}`);
  };

  return { filterValues, searchParams, onFilterClick };
}
