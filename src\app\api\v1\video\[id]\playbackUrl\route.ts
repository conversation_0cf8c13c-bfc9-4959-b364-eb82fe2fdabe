import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { db } from "~/server/db";
import { videos } from "~/server/db/schema";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { refreshMUXToken } from "~/server/utils/mux";
import { checkToken } from "~/server/utils/permissions";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/playbackUrl:
 *   get:
 *     description: Get video playback url.
 *     tags: [Video playback]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *        200:
 *          description: Video source url
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  videoUrl:
 *                    type: string
 *                  thumbnailUrl:
 *                    type: string
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;

  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  try {
    const { tokenPayload, token } = await checkToken(request);

    const video = await getVideoByIdForPermissionCheck(
      id,
      tokenPayload.roles,
      token,
    );

    if (!video?.fullFilename) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    if (!video.muxPlaybackId) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Video doesnt exist",
      });
    }
    const checkVideoToken = await refreshMUXToken({
      aud: "video",
      muxPlaybackId: video.muxPlaybackId,
      oldToken: video.playbackToken,
    });

    const playbackToken = checkVideoToken.newToken ?? checkVideoToken.oldToken;
    let thumbnail = video.thumbnail
      ? video.thumbnail.split("?")[0]
      : `https://image.mux.com/${video.muxPlaybackId}/thumbnail.png`;

    const checkThumbnailToken = await refreshMUXToken({
      aud: "thumbnail",
      muxPlaybackId: video.muxPlaybackId,
      oldToken: video.thumbnailToken,
    });
    if (!!checkVideoToken.newToken || !!checkThumbnailToken.newToken) {
      await db
        .update(videos)
        .set({
          playbackToken: checkVideoToken.newToken,
          thumbnailToken: checkThumbnailToken.newToken,
        })
        .where(eq(videos.id, id));
    }
    thumbnail += `?token=${checkThumbnailToken.oldToken ?? checkThumbnailToken.newToken}`;

    return Response.json({
      videoUrl: `https://stream.mux.com/${video.muxPlaybackId}.m3u8?token=${playbackToken}`,
      thumbnailUrl: thumbnail,
    });
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
