import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { UserRole } from "~/lib/enums";
import { db } from "~/server/db";
import { videoAthletes, type VideoAthlete } from "~/server/db/schema";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/athletes:
 *   put:
 *     description: |
 *       Update video athletes.
 *
 *       **Roles required:** api, api_modify, admin / analyst
 *     tags: [Update video]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - athletes
 *             properties:
 *               athletes:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     athleteId:
 *                       type: string
 *     responses:
 *       200:
 *         description: Video summary
 *         content:
 *           application/json:
 *             schema:
 *                type: array
 *                items:
 *                  type: object
 *                  properties:
 *                    athleteId:
 *                      type: string
 *                    isHp:
 *                      type: boolean
 *                    name:
 *                      type: string
 */

interface UpdateVideoAthletes {
  athletes: VideoAthlete[];
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;

  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  const body = (await request.json()) as UpdateVideoAthletes;

  try {
    const { tokenPayload, token } = await checkToken(request, [
      UserRole.api_modify,
    ]);
    await getVideoByIdForPermissionCheck(id, tokenPayload.roles, token);

    const updatedAthletes = await db.transaction(async (tx) => {
      await tx.delete(videoAthletes).where(eq(videoAthletes.videoId, id));
      await tx
        .insert(videoAthletes)
        .values(body.athletes.map((athlete) => ({ ...athlete, videoId: id })));

      return await tx.query.videoAthletes.findMany({
        where: (fields) => eq(fields.videoId, id),
        columns: {
          athleteId: true,
        },
      });
    });

    return Response.json(updatedAthletes);
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
