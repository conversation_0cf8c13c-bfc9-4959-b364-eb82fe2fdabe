import { <PERSON><PERSON><PERSON>, <PERSON>tings, User, UserRoundCog, Users } from "lucide-react";
import { AvatarFallback, AvatarImage, Avatar } from "~/components/ui/avatar";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { getAvatarLettersByUsername } from "~/lib/utils";
import { getServerAuthSession } from "~/server/auth";
import { env } from "~/env";
import { SignoutButton } from "./SignoutButton";
import { LogOut } from "lucide-react";
import { UserRole } from "~/lib/enums";

export async function UserButton() {
  const session = await getServerAuthSession();
  const isAdmin = session?.user.roles.includes(UserRole.admin);
  const hasApiPermission =
    !!isAdmin || session?.user.roles.includes(UserRole.api);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer">
          <AvatarImage src={session?.user.image ?? ""} />
          <AvatarFallback className="text-black">
            {getAvatarLettersByUsername(session?.user.name)}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <User className="mr-2 h-4 w-4" />
            <Link
              href={`${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/realms/hpsnz/account`}
              target="_blank"
            >
              Manage My Account
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <Link href="/account/videos">Manage Videos</Link>
          </DropdownMenuItem>
          {hasApiPermission && (
            <DropdownMenuItem>
              <FileCode className="mr-2 h-4 w-4" />
              <Link href="/api-doc">Api docs</Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem>
            <LogOut className="mr-2 h-4 w-4" />
            <SignoutButton />
          </DropdownMenuItem>
        </DropdownMenuGroup>
        {isAdmin && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <Users className="mr-2 h-4 w-4" />
                <Link
                  href={`${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/admin/hpsnz/console/#/hpsnz/users`}
                  target="_blank"
                >
                  Manage Users
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <UserRoundCog className="mr-2 h-4 w-4" />
                <Link
                  href={`${env.NEXT_PUBLIC_PTA_ROOT_URL}/account/permissions/`}
                  target="_blank"
                >
                  Add/Remove Users
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
