import { env } from "~/env";
import * as jwt from "jsonwebtoken";
import type { FitMode } from "~/lib/enums";
import type { DecodedToken, ThumnailParams } from "~/lib/interface";
import Mux from "@mux/mux-node";
import dayjs from "dayjs";

/**
 *
 * v (Video or Subtitles/Closed Captions)
 *
 * t (Thumbnail)
 *
 * g (GIF)
 *
 * s (Storyboard)
 * @description Refresh MUX token if the old token is not valid or expired, token expiration time is 1 day
 */
export const refreshMUXToken = async ({
  aud,
  muxPlaybackId,
  oldToken,
  thumbnailUrl,
}: {
  aud: "video" | "thumbnail" | "gif" | "storyboard";
  muxPlaybackId: string;
  oldToken?: string | null;
  thumbnailUrl?: string | null;
}) => {
  if (oldToken) {
    const decodedToken = jwt.decode(oldToken) as DecodedToken;
    const exp = decodedToken?.exp;
    const expireDate = new Date(exp * 1000);

    const tomorrow = dayjs().add(1, "day").toDate();

    if (expireDate > tomorrow) {
      return { oldToken };
    }
  }

  const thumnailParams: ThumnailParams = {};
  if (thumbnailUrl) {
    const params = getThumnailParams(thumbnailUrl);

    if (params.rotate && [90, 180, 270].includes(+params.rotate)) {
      thumnailParams.rotate = +params.rotate as 90 | 180 | 270;
    }
    if (params.time) {
      thumnailParams.time = +params.time;
    }
    if (params.width) {
      thumnailParams.width = +params.width;
    }
    if (params.height) {
      thumnailParams.height = +params.height;
    }
    if (params.fitMode) {
      thumnailParams.fit_mode = params.fitMode as FitMode;
    }
  } else {
    thumnailParams.time = 0;
  }

  const newToken = await generateMUXToken({
    aud,
    muxPlaybackId,
    params: aud === "thumbnail" ? thumnailParams : undefined,
  });
  return { newToken };
};

/**
 *
 * v (Video or Subtitles/Closed Captions)
 *
 * t (Thumbnail)
 *
 * g (GIF)
 *
 * s (Storyboard)
 * @param exp E.g 60, "2 days", "10h", "7d", numeric value interpreted as seconds
 */
export const generateMUXToken = async ({
  aud,
  muxPlaybackId,
  exp = "3d",
  params,
}: {
  aud: "video" | "thumbnail" | "gif" | "storyboard";
  muxPlaybackId: string;
  exp?: string;
  params?: ThumnailParams;
}) => {
  const secretKey = Buffer.from(env.MUX_SIGNING_KEY_SECRET, "base64").toString(
    "ascii",
  );

  const mux = new Mux({
    tokenId: env.MUX_ACCESS_TOKEN_ID,
    tokenSecret: env.MUX_SECRET_KEY,
  });

  const restrictedParams =
    params && aud === "thumbnail"
      ? {
          ...(params as Record<string, string>),
        }
      : {
          playback_restriction_id: env.MUX_RESTRICTION_ID,
        };

  const token = await mux.jwt.signPlaybackId(muxPlaybackId, {
    type: aud,
    keyId: env.MUX_SIGNING_KEY_ID,
    keySecret: secretKey,
    expiration: exp,
    params: restrictedParams,
  });

  return token;
};

export const generateVideoThumbnail = ({
  playbackId,
  params,
}: {
  playbackId: string;
  params: {
    time: number;
    width?: number;
    height?: number;
    rotate?: number;
    fit_mode?: "preserve" | "stretch" | "crop" | "smartcrop" | "pad";
  };
}) => {
  const url = new URL(`https://image.mux.com/${playbackId}/thumbnail.png`);
  const urlParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      urlParams.append(key, value.toString());
    }
  });

  // const token = generateMUXToken("t", playbackId);

  // urlParams.append("token", token);

  url.search = urlParams.toString();

  return url.toString();
};

export const getThumnailParams = (url?: string | null) => {
  if (!url)
    return {
      time: null,
      width: null,
      height: null,
      rotate: null,
      fitMode: null,
    };

  const urlObj = new URL(url);
  const time = urlObj.searchParams.get("time");
  const width = urlObj.searchParams.get("width");
  const height = urlObj.searchParams.get("height");
  const rotate = urlObj.searchParams.get("rotate");
  const fitMode = urlObj.searchParams.get("fit_mode") as FitMode | null;

  return { time, width, height, rotate, fitMode };
};
