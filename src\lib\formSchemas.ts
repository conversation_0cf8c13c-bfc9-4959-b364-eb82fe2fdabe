import { z } from "zod";
import { fitModes, sports, videoPermissions, videoStatuses } from "./enums";

export const addThumbnailFormSchema = z.object({
  time: z.string().optional(),
  width: z.string().optional(),
  height: z.string().optional(),
  rotate: z.string().optional(),
  fit_mode: z.enum(fitModes).optional(),
});

export const videoEditFormSchema = z.object({
  title: z.string().min(1, {
    message: "Title must be at least 1 characters",
  }),
  sport: z.enum(sports, {
    required_error: "Please select a sport",
  }),
  permission: z.enum(videoPermissions),
  status: z.enum(videoStatuses),
  //thumbnail
  time: z.string().optional(),
  width: z.string().optional(),
  height: z.string().optional(),
  rotate: z.string().optional(),
  fit_mode: z.enum(fitModes).optional(),
  videoDate: z.date().optional(),
});

export const competitionFormSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string().nullish(),
  date: z.string().nullish(),
  location: z.string().nullish(),
  sport: z.enum(sports),
  isOfficial: z.boolean().nullish(),
});

export type Competition = z.infer<typeof competitionFormSchema>;
