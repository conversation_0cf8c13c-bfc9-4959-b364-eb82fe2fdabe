"use client";

import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { Tag } from "./Tag";
import { Button } from "~/components/ui/button";

export const FilterTags = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const sports = searchParams.getAll("sports");
  const status = searchParams.getAll("status");
  const metadata = searchParams.getAll("metadata");

  const onTagClose = (type: "sports" | "status" | "metadata", tag: string) => {
    const params = new URLSearchParams(searchParams.toString());
    const newValues = params.getAll(type).filter((v) => v !== tag);
    params.delete(type);
    if (type === "sports") {
      params.delete("metadata");
    }
    newValues.forEach((v) => {
      params.append(type, v);
    });
    router.push(`${pathname}?${params.toString()}`);
  };

  const onClearFilter = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("sports");
    params.delete("status");
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex flex-wrap items-center gap-3">
      <Button variant="outline" onClick={onClearFilter}>
        Clear Filters
      </Button>
      {sports?.map((sport) => (
        <Tag
          key={sport}
          label={sport}
          onClose={() => onTagClose("sports", sport)}
        />
      ))}
      {status?.map((status) => (
        <Tag
          key={status}
          label={status}
          onClose={() => onTagClose("status", status)}
        />
      ))}
      {metadata?.map((tag) => (
        <Tag
          key={tag}
          label={tag}
          onClose={() => onTagClose("metadata", tag)}
        />
      ))}
    </div>
  );
};
