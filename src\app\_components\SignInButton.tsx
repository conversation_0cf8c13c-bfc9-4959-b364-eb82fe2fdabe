"use client";

import { signIn } from "next-auth/react";
import { Button } from "~/components/ui/button";
export interface ProviderProps {
  id: string;
  name: string;
  type: "oauth";
  signinUrl: string;
  callbackUrl: string;
}
export const SignInButton = ({ provider }: { provider: ProviderProps }) => {
  return (
    <Button
      onClick={() => signIn(provider.id)}
      className="relative inline-flex h-12 items-center justify-center overflow-hidden rounded-xl border border-gray-800 bg-gradient-to-r from-gray-800 to-gray-950 px-6 font-medium text-white shadow-2xl transition-colors  hover:opacity-90 hover:brightness-110"
    >
      Sign in
    </Button>
  );
};
