"use client";

import { EllipsisVertical } from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import toast from "react-hot-toast";
import { Button } from "~/components/ui/button";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { UserRole } from "~/lib/enums";
import { downloadFileFromUrl } from "~/lib/file";
import { getUserSports } from "~/lib/roles";
import { type VideoAllOutput } from "~/server/api/routers/video";
import { api } from "~/trpc/react";
import { TriggerAIButton } from "./TriggerAIButton";

export function EllipsesEdit({
  video,
}: {
  video: VideoAllOutput["videoList"][number];
}) {
  const videoId = video.id;
  const createById = video.createdById;
  const videoName =
    video.filename ?? video.fullFilename ?? video.title ?? "unknown";
  const sport = video.sport;

  const { data: session } = useSession();
  const isAdmin = session?.user.roles.includes(UserRole.admin);

  const isAnalyst = session?.user.roles.includes(UserRole.analyst);
  const analystSports = getUserSports(session?.user.roles as UserRole[]);
  const analystCanEdit = isAnalyst && sport && analystSports.includes(sport);

  const isOwner =
    !!isAdmin || session?.user?.id === createById || analystCanEdit;
  const canDownload = true;
  // const canDownload = !!isOwner || session?.user.roles.includes(UserRole.analyst);

  const { mutate: generateDownloadUrl, isPending } =
    api.video.generateDownloadUrl.useMutation({
      onError: () => {
        toast.error("Failed to generate download link");
      },
      onSuccess: async (url) => {
        downloadFileFromUrl(url, videoName);
        toast.success("Video start download");
      },
    });

  const onDownload = () => {
    generateDownloadUrl({ videoId });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <EllipsisVertical className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {isOwner && (
          <DropdownMenuItem>
            <Link
              href={`/account/videos/${videoId}`}
              className="w-full"
              prefetch={false}
            >
              Edit
            </Link>
          </DropdownMenuItem>
        )}
        {canDownload && (
          <DropdownMenuItem>
            <button
              // variant="ghost"
              className="w-full text-left"
              disabled={isPending}
              onClick={onDownload}
            >
              Download
            </button>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem>
          <TriggerAIButton video={video} className="w-full text-left" />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
