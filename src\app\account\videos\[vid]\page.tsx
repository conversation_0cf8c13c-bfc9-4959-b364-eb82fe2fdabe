import { TRPCError } from "@trpc/server";
import { BackButton } from "~/app/_components/BackButton";
import { ErrorBox } from "~/app/_components/ErrorBox";
import { TriggerAIButton } from "~/app/_components/TriggerAIButton";
import { AccountVideoEdit } from "~/app/_components/account/VideoEdit";
import { api } from "~/trpc/server";

//force update if there are data changes in db
export const dynamic = "force-dynamic";

export default async function AccountVideoPage({
  params,
}: {
  params: { vid: string };
}) {
  try {
    const id = params.vid;
    const data = await api.account.videoById({ id });

    return (
      <main>
        <div className="flex flex-row items-center justify-between pr-12">
          <BackButton />
          <TriggerAIButton video={data} />
        </div>
        <div className="flex flex-col items-center justify-center xl:p-16">
          {data && <AccountVideoEdit video={data} />}
        </div>
      </main>
    );
  } catch (error) {
    if (error instanceof TRPCError) {
      return <ErrorBox code={error.code} message={error.message} />;
    } else {
      return <div>An unexpected error occurred</div>;
    }
  }
}
