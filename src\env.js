import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    DATABASE_URL: z.string().url(),
    NODE_ENV: z
      .enum(["development", "test", "production"])
      .default("development"),
    NEXTAUTH_SECRET:
      process.env.NODE_ENV === "production"
        ? z.string()
        : z.string().optional(),
    NEXTAUTH_URL: z.preprocess(
      // This makes Vercel deployments not fail if you don't set NEXTAUTH_URL
      // Since NextAuth.js automatically uses the VERCEL_URL if present.
      (str) => process.env.VERCEL_URL ?? str,
      // VERCEL_URL doesn't include `https` so it cant be validated as a URL
      process.env.VERCEL ? z.string() : z.string().url(),
    ),
    KEYCLOAK_ID: z.string().min(1),
    K<PERSON>YCLOAK_SECRET: z.string().min(1),
    KEYCLOAK_VIDEO_PLATFORM_API_CLIENT_SECRET: z.string().min(1),
    AWS_ACCESS_KEY_ID: z.string().min(1),
    AWS_SECRET_ACCESS_KEY: z.string().min(1),
    AWS_BUCKET_NAME: z.string().min(1),
    AWS_LAMBDA_CONVERTER_URL: z.string().min(1),
    MUX_ACCESS_TOKEN_ID: z.string().min(1),
    MUX_SECRET_KEY: z.string().min(1),
    MUX_SIGNING_KEY_ID: z.string().min(1),
    MUX_SIGNING_KEY_SECRET: z.string().min(1),
    MUX_RESTRICTION_ID: z.string().min(1),
    CLOUDFRONT_KEY_PAIR_ID: z.string().min(1),
    CLOUDFRONT_PRIVATE_KEY: z.string().min(1),
    CLOUDFRONT_DOMAIN: z.string().min(1),
    ENABLE_VIDEO_CONVERTER: z.string().optional(),
    TAGGER_URL: z.string().min(1),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
    NEXT_PUBLIC_KEYCLOAK_ROOT: z.string().min(1),
    NEXT_PUBLIC_PTA_ROOT_URL: z.string().min(1),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    NODE_ENV: process.env.NODE_ENV,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    ENABLE_VIDEO_CONVERTER: process.env.ENABLE_VIDEO_CONVERTER,
    KEYCLOAK_ID: process.env.KEYCLOAK_ID,
    KEYCLOAK_SECRET: process.env.KEYCLOAK_SECRET,
    NEXT_PUBLIC_KEYCLOAK_ROOT: process.env.NEXT_PUBLIC_KEYCLOAK_ROOT,
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
    MUX_ACCESS_TOKEN_ID: process.env.MUX_ACCESS_TOKEN_ID,
    MUX_SECRET_KEY: process.env.MUX_SECRET_KEY,
    MUX_SIGNING_KEY_ID: process.env.MUX_SIGNING_KEY_ID,
    MUX_SIGNING_KEY_SECRET: process.env.MUX_SIGNING_KEY_SECRET,
    MUX_RESTRICTION_ID: process.env.MUX_RESTRICTION_ID,
    NEXT_PUBLIC_PTA_ROOT_URL: process.env.NEXT_PUBLIC_PTA_ROOT_URL,
    CLOUDFRONT_KEY_PAIR_ID: process.env.CLOUDFRONT_KEY_PAIR_ID,
    CLOUDFRONT_PRIVATE_KEY: process.env.CLOUDFRONT_PRIVATE_KEY,
    CLOUDFRONT_DOMAIN: process.env.CLOUDFRONT_DOMAIN,
    AWS_LAMBDA_CONVERTER_URL: process.env.AWS_LAMBDA_CONVERTER_URL,
    TAGGER_URL: process.env.TAGGER_URL,
    KEYCLOAK_VIDEO_PLATFORM_API_CLIENT_SECRET:
      process.env.KEYCLOAK_VIDEO_PLATFORM_API_CLIENT_SECRET,
  },
  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  /**
   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
   * `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
});
