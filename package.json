{"name": "hpsnz-web-video", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm next-swagger-doc-cli next-swagger-doc.json && next build", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:studio": "drizzle-kit studio", "db:migrate": "drizzle-kit migrate", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@auth/drizzle-adapter": "^0.7.0", "@aws-sdk/client-s3": "^3.556.0", "@aws-sdk/cloudfront-signer": "^3.621.0", "@aws-sdk/s3-request-presigner": "^3.556.0", "@hookform/resolvers": "^3.3.4", "@mux/mux-node": "^8.8.0", "@mux/mux-player-react": "^2.7.0", "@paralleldrive/cuid2": "^2.2.2", "@planetscale/database": "^1.16.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@t3-oss/env-nextjs": "^0.9.2", "@tanstack/react-query": "^5.25.0", "@tanstack/react-table": "^8.16.0", "@trpc/client": "next", "@trpc/next": "next", "@trpc/react-query": "next", "@trpc/server": "next", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "drizzle-orm": "^0.29.4", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "lucide-react": "^0.368.0", "mysql2": "^3.9.1", "next": "14.1.4", "next-auth": "^4.24.6", "next-swagger-doc": "^0.4.0", "react": "18.2.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.51.3", "react-hot-toast": "^2.4.1", "server-only": "^0.0.1", "superjson": "^2.2.1", "swagger-ui-react": "5.17.2", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/eslint": "^8.56.2", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.11.20", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@types/swagger-ui-react": "^4.18.3", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "drizzle-kit": "^0.21.1", "eslint": "^8.57.0", "eslint-config-next": "^14.1.3", "eslint-plugin-drizzle": "^0.2.3", "postcss": "^8.4.34", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.4.2"}, "ct3aMetadata": {"initVersion": "7.30.1"}, "packageManager": "pnpm@8.4.0"}