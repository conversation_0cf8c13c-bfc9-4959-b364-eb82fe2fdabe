import "~/styles/globals.css";

import { Inter } from "next/font/google";
import { type Metadata } from "next";
import { TRPCReactProvider } from "~/trpc/react";
import { Toaster } from "react-hot-toast";
import { Navbar } from "./_components/Navbar";
import { getServerAuthSession } from "~/server/auth";
import SessionProvider from "./SessionProvider";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "HPSNZ Video",
  description: "HPSNZ Video",
  icons: [{ rel: "icon", url: "/images/icon.png" }],
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerAuthSession();
  // console.log(session);
  // if (!session?.user.name?.includes("admin")) {
  //   console.log(session);
  // }

  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/images/icon.png" sizes="any" />
      </head>
      <body
        className={`font-sans ${inter.variable} h-screen bg-black text-white`}
      >
        <SessionProvider>
          <TRPCReactProvider>
            <div className="flex h-full flex-col">
              {session && (
                <div className="sticky top-0 z-50 h-[80px] w-full bg-background">
                  <Navbar />
                </div>
              )}
              <div className="flex-1">{children}</div>
            </div>
          </TRPCReactProvider>
          <Toaster />
        </SessionProvider>
      </body>
    </html>
  );
}
