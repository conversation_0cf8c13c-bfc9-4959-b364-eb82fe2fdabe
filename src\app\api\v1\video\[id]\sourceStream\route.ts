import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { TRPCError } from "@trpc/server";
import dayjs from "dayjs";
import { NextResponse, type NextRequest } from "next/server";
import type { Readable } from "stream";
import { env } from "~/env";
import { s3Client } from "~/server/api/routers/aws";
import { checkIfFileExists } from "~/server/utils/aws";
import { getCloudFrontSignedUrl } from "~/server/utils/cloudFront";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

export const maxDuration = 30;
/**
 * @swagger
 * /api/v1/video/{id}/sourceStream:
 *   get:
 *     description: Get video stream optimised for seeking.
 *     tags: [Video playback]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *        200:
 *          description: The video source stream file (m3u8) with signed URLs for each segment OR a faststart enabled MP4, depending on original video format.
 *          content:
 *            application/x-mpegURL:
 *              schema:
 *                type: string
 *                format: binary
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  videoUrl:
 *                    type: string
 *                  cacheUrl:
 *                    type: string
 *                  expiry:
 *                    type: string
 */
const streamToString = (stream: Readable): Promise<string> =>
  new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    stream.on("data", (chunk: Buffer) => chunks.push(chunk));
    stream.on("error", reject);
    stream.on("end", () => resolve(Buffer.concat(chunks).toString("utf8")));
  });

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;
  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  try {
    const { tokenPayload, token } = await checkToken(request);

    const video = await getVideoByIdForPermissionCheck(
      id,
      tokenPayload.roles,
      token,
    );

    if (!video?.fullFilename) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    //get m3u8 file name
    const folderPath = video.fullFilename.split("/").slice(0, 2).join("/");
    const objectPathKey = `${folderPath}/${id}/stream`;
    const mp4FilePath = `${objectPathKey}/output.mp4`;
    const m3u8FilePath = `${objectPathKey}/stream.m3u8`;
    const initMp4FilePath = `${objectPathKey}/init.mp4`;
    const existInitMp4 = await checkIfFileExists(
      env.AWS_BUCKET_NAME,
      initMp4FilePath,
    );

    const existMp4 = await checkIfFileExists(env.AWS_BUCKET_NAME, mp4FilePath);

    if (existMp4) {
      const getObjectParams = {
        Bucket: env.AWS_BUCKET_NAME,
        Key: mp4FilePath,
      };
      const getObjectCommand = new GetObjectCommand(getObjectParams);

      const videoPresignedUrl = await getSignedUrl(s3Client, getObjectCommand, {
        expiresIn: 3600 * 24,
      });

      const cacheUrl = getCloudFrontSignedUrl(mp4FilePath);

      return Response.json({
        videoUrl: videoPresignedUrl,
        cacheUrl,
        expiry: dayjs().add(1, "day").toISOString(),
      });
    }

    const getObjectParams = {
      Bucket: env.AWS_BUCKET_NAME,
      Key: m3u8FilePath,
    };

    const getStreamCommand = new GetObjectCommand(getObjectParams);

    const response = await s3Client.send(getStreamCommand);

    const content = await streamToString(response.Body as Readable);

    if (!content || content.length === 0) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No content found in the playlist.",
      });
    }

    if (content.includes('EXT-X-MAP:URI="init.mp4"') && !existInitMp4) {
      const signedUrl = getCloudFrontSignedUrl(initMp4FilePath);
      content.replace("init.mp4", signedUrl);
    }

    const pattern = /data_\S+_\d+.ts/g;
    const matches = content.match(pattern);

    if (matches) {
      let modifiedContent = content;
      if (
        modifiedContent.includes('EXT-X-MAP:URI="init.mp4"') &&
        existInitMp4
      ) {
        const signedUrl = getCloudFrontSignedUrl(initMp4FilePath);
        modifiedContent = modifiedContent.replace("init.mp4", signedUrl);
      }

      for (const match of matches) {
        const dataKey = `${objectPathKey}/${match}`;
        const signedUrl = getCloudFrontSignedUrl(dataKey);
        modifiedContent = modifiedContent.replace(match, signedUrl);
      }

      // Respond with the modified content
      const blob = new Blob([modifiedContent], {
        type: "application/x-mpegURL",
      });

      // Return the file as a response with correct headers
      return new NextResponse(blob, {
        headers: {
          "Content-Type": "application/x-mpegURL",
          "Content-Disposition":
            'attachment; filename="modified_stream_playlist.m3u8"',
        },
      });
    } else {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No matching patterns found in the playlist.",
      });
    }
  } catch (cause) {
    console.log(cause);
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
