import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import { convertVideo } from "~/server/utils/aws";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/convert:
 *   post:
 *     description: |
 *       To trigger video conversion. It happens automatically on upload, but can be triggered manually.
 *
 *       **Roles required:** api, admin / analyst
 *     tags: [Upload]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               forceReplace:
 *                 type: boolean
 *                 default: false
 *               createSourceStream:
 *                 type: boolean
 *               encodeAdaptiveStreams:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Video summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */

interface Body {
  forceReplace?: boolean;
  createSourceStream?: boolean;
  encodeAdaptiveStreams?: boolean;
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;
  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "No video ID",
    });
  }

  let forceReplace = false;
  let createSourceStream = true;

  const contentType = request.headers.get("content-type") ?? "";

  try {
    //check if body is json
    if (contentType.includes("application/json")) {
      const body = (await request.json()) as Body;
      if (body.createSourceStream) createSourceStream = body.createSourceStream;
      if (body.forceReplace) forceReplace = body.forceReplace;
    }
  } catch (error) {
    console.error(error);
  }
  try {
    const { tokenPayload, token } = await checkToken(request);

    const video = await getVideoByIdForPermissionCheck(
      id,
      tokenPayload.roles,
      token,
    );

    if (!video?.fullFilename) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    const response = await convertVideo({
      videoId: id,
      fullFilename: video.fullFilename,
      forceReplace,
      createSourceStream,
    });

    const resText = await response.text();
    if (resText.includes("SyntaxError") || resText.includes("EncodingError")) {
      return Response.json(
        { message: resText },
        {
          status: 500,
        },
      );
    }

    // get streaming logs (deprecated) (it takes too long to wait for the whole response)
    // const reader = response.body?.getReader();
    // const decoder = new TextDecoder();
    // let responseData = "";
    // if (reader) {
    //   while (true) {
    //     const { value, done } = await reader.read();
    //     if (done) {
    //       break;
    //     }
    //     // Decode and accumulate the chunks
    //     responseData += decoder.decode(value, { stream: true });
    //   }
    // }
    // console.log(responseData);

    return Response.json(
      { message: "Success - fully started" },
      { status: 200 },
    );
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
