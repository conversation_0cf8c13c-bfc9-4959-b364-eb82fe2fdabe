"use client";

import type { AccountVideosOutput } from "~/server/api/routers/account";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  ChevronDown,
  CircleCheck,
  CirclePause,
  MoreHorizontal,
} from "lucide-react";

import { Button } from "~/components/ui/button";
import { Checkbox } from "~/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { useState } from "react";
import Link from "next/link";
import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ConfirmButton } from "../ConfirmButton";
import {
  UserRole,
  booleans,
  sportOptions,
  videoPermissionOptions,
} from "~/lib/enums";
import type { Session } from "next-auth";
import { type Option, TableFilter } from "../TableFilter";
import { Tag } from "../Tag";
import { TriggerAIButton } from "../TriggerAIButton";

type Column = AccountVideosOutput["videoList"][number];

export const AccountVideoList = ({
  session,
  videos,
}: {
  session: Session | null;
  videos: AccountVideosOutput;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    muxPlaybackId: false,
  });
  const [rowSelection, setRowSelection] = useState({});
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [publishOpen, setPublishOpen] = useState(false);
  const [unpublishOpen, setUnpublishOpen] = useState(false);

  const currentPage = +(searchParams.get("page") ?? 1);
  const pageSize = +(searchParams.get("pageSize") ?? 10);
  const metadata = searchParams.getAll("metadata");
  const offset = currentPage * pageSize;
  const videoCount = videos.videosCount[0]?.count ?? 0;

  const isAdmin = session?.user.roles.includes(UserRole.admin);
  const isAnalyst = session?.user.roles.includes(UserRole.analyst);

  const selectedIds = Object.entries(rowSelection)
    .filter(([_, value]) => !!value)
    .map(([key]) => key);
  const hasSelectedRows = selectedIds.length > 0;

  const createByColumn: ColumnDef<Column> = {
    accessorKey: "user.name",
    header: () => <div className="ml-auto">Created By</div>,
    cell: ({ getValue }) => {
      const value = getValue() as string;
      return <div className="text-right font-medium">{value}</div>;
    },
  };
  const actionColumn: ColumnDef<Column> = {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const id = row.id;
      const isDraft = !!row.getValue("isDraft");
      const muxPlaybackId: string | null = row.getValue("muxPlaybackId");

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Link
                href={`/account/videos/${id}`}
                className="w-full"
                prefetch={false}
              >
                Edit
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <TriggerAIButton
                video={{
                  id: row.id,
                  status: row.getValue("status"),
                  sport: row.getValue("sport"),
                  tags: row.original.tags,
                }}
              />
            </DropdownMenuItem>
            <DropdownMenuItem
              disabled={!muxPlaybackId || isPendingPublishVideo}
              onClick={() =>
                publishVideo({ videoIds: [id], isDraft: !isDraft })
              }
            >
              {isDraft ? "Publish" : "Unpublish"}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              disabled={isPendingDeleteVideo}
              onClick={() => onDeleteVideos([id])}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  };

  const baseColumns: ColumnDef<Column>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value: boolean) =>
            table.toggleAllPageRowsSelected(!!value)
          }
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value: boolean) => {
            row.toggleSelected(!!value);
          }}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue("status")}</div>
      ),
    },
    {
      accessorKey: "title",
      header: () => <div>Title</div>,
      cell: ({ row }) => (
        <div className="lowercase">{row.getValue("title")}</div>
      ),
    },
    {
      accessorKey: "sport",
      header: () => <div className="ml-auto">Sport</div>,
      cell: ({ row }) => (
        <div className="text-right font-medium">{row.getValue("sport")}</div>
      ),
      meta: {
        paramKey: "sports",
        filterOptions: sportOptions,
      },
    },
    {
      accessorKey: "permission",
      header: () => <div className="ml-auto">Permission</div>,
      cell: ({ row }) => (
        <div className="text-right font-medium">
          {row.getValue("permission")}
        </div>
      ),
      meta: {
        paramKey: "permission",
        filterOptions: videoPermissionOptions,
      },
    },
    {
      accessorKey: "isDraft",
      header: () => <div className="ml-auto">Published</div>,
      cell: ({ row }) => {
        const published = !row.getValue("isDraft");
        return (
          <div className="flex justify-end">
            {published && <CircleCheck className="h-4 w-4 text-green" />}
            {!published && <CirclePause className="h-4 w-4 text-yellow-500" />}
          </div>
        );
      },
      meta: {
        paramKey: "published",
        filterOptions: booleans,
      },
    },
    {
      accessorKey: "muxPlaybackId",
      header: () => <div className="ml-auto">MUX Playback ID</div>,
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return <div className="text-right font-medium">{value}</div>;
      },
      enableGlobalFilter: false,
    },
  ];

  const showCreateBy = !!isAdmin || isAnalyst;
  const columns = showCreateBy
    ? [...baseColumns, createByColumn, actionColumn]
    : [...baseColumns, actionColumn];

  const table = useReactTable({
    data: videos.videoList,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getRowId: (row) => row.id,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const { mutateAsync: deleteVideo, isPending: isPendingDeleteVideo } =
    api.video.delete.useMutation({
      onSuccess: () => {
        setRowSelection({});
        router.refresh();
      },
    });

  const { mutateAsync: publishVideo, isPending: isPendingPublishVideo } =
    api.video.publish.useMutation({
      onSuccess: () => router.refresh(),
    });

  const onDeleteVideos = async (ids: string[]) => {
    await toast.promise(deleteVideo(ids), {
      loading: "Deleting videos...",
      success: "Videos deleted successfully.",
      error: (e: { message: string }) => e.message,
    });
  };

  const onPublishVideos = async (ids: string[]) => {
    await toast.promise(publishVideo({ videoIds: ids, isDraft: false }), {
      loading: "Publishing videos...",
      success: "Videos published successfully.",
      error: (e: { message: string }) => e.message,
    });
  };

  const onUnPublishVideos = async (ids: string[]) => {
    await toast.promise(publishVideo({ videoIds: ids, isDraft: true }), {
      loading: "Unpublishing videos...",
      success: "Videos unpublished successfully.",
      error: (e: { message: string }) => e.message,
    });
  };

  const onMetadataRemove = (tag: string) => {
    const params = new URLSearchParams(searchParams.toString());
    const newValues = params.getAll("metadata").filter((v) => v !== tag);
    params.delete("metadata");
    newValues.forEach((v) => {
      params.append("metadata", v);
    });
    router.push(`${pathname}?${params.toString()}`);
  };

  const onPageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex w-full flex-col overflow-x-auto border-2">
      <div className="grid grid-cols-2 gap-4 py-4 md:grid-cols-2 md:justify-between">
        <div className="flex items-center space-x-2">
          <Link href="/account/videos/new">
            <Button variant="outline" className="w-full md:w-auto">
              Upload Video
            </Button>
          </Link>
          <div className="flex gap-1">
            {metadata?.map((tag) => (
              <Tag
                key={tag}
                label={tag}
                onClose={() => onMetadataRemove(tag)}
              />
            ))}
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full md:ml-auto md:w-auto">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="h-[400px] overflow-y-auto rounded-md border lg:h-[600px] 2xl:h-[800px]">
        <Table className="bg-black">
          <TableHeader className=" sticky top-0">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const column = header.column;
                  const meta = column.columnDef.meta as
                    | {
                        paramKey: string;
                        filterOptions: Option<string>[];
                      }
                    | undefined;

                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : (
                        <div className="flex items-center gap-2">
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                          {meta?.paramKey && (
                            <TableFilter
                              paramKey={meta.paramKey}
                              options={meta.filterOptions}
                            />
                          )}
                        </div>
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-white">
          {selectedIds.length} of {videoCount} row(s) selected.
        </div>
        <div className="space-x-2">
          <ConfirmButton
            label="Unpublish"
            description="Are you sure you want to unpublish all selected videos?"
            open={unpublishOpen}
            setOpen={setUnpublishOpen}
            disabled={!hasSelectedRows || isPendingPublishVideo}
            onConfirm={() => {
              setUnpublishOpen(false);
              void onUnPublishVideos(selectedIds);
            }}
          />
          <ConfirmButton
            label="Publish"
            description="Are you sure you want to publish all selected videos?"
            open={publishOpen}
            setOpen={setPublishOpen}
            disabled={!hasSelectedRows || isPendingPublishVideo}
            onConfirm={() => {
              setPublishOpen(false);
              void onPublishVideos(selectedIds);
            }}
          />
          <ConfirmButton
            label="Delete"
            description="Are you sure you want to delete all selected videos?"
            open={deleteOpen}
            danger
            setOpen={setDeleteOpen}
            disabled={!hasSelectedRows || isPendingDeleteVideo}
            onConfirm={() => {
              setDeleteOpen(false);
              void onDeleteVideos(selectedIds);
            }}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            Previous
          </Button>
          {currentPage}/{Math.max(1, Math.ceil(videoCount / pageSize))}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={offset >= videoCount}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};
