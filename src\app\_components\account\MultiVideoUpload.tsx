"use client";

import { useSession } from "next-auth/react";
import { type ChangeEvent, useState, Fragment } from "react";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  type Sport,
  type VideoPermission,
  videoPermissions,
  type UserRole,
  VideoStatus,
} from "~/lib/enums";
import { getUserSports } from "~/lib/roles";
import { AccountVideoUploadItem } from "./VideoUploadItem";
import { cn } from "~/lib/utils";
import { api } from "~/trpc/react";
import toast from "react-hot-toast";
import { Button } from "~/components/ui/button";
import Divider from "../Divider";
import { usePreventUnload } from "~/hooks/usePreventRouteChange";
import { Metadata } from "../Metadata";
import { CompetitionSelect } from "../CompetitionSelect";
import type { Competition } from "~/lib/formSchemas";
import { AthleteSelect } from "../AthleteSelect";
import type { VideoUpdateInput } from "~/server/api/routers/video";

export interface MultiUploadFile {
  key: string;
  file: File;
  title: string;
  permission: VideoPermission;
  sport: Sport;
  status: VideoStatus;
  muxPlaybackId?: string | null;
  //video
  id?: string;
  competition?: {
    id: string;
    name: string;
    type?: string | null;
    location?: string | null;
    sport: Sport;
    isOfficial?: boolean | null;
    date?: string | null;
  };
  athletes?: {
    athleteId: string;
    isHp: boolean;
    name: string;
  }[];
  metadata?: string[];
  videoDate?: Date;
}

export const MultiVideoUpload = () => {
  const { data: session } = useSession();
  const roleSports = getUserSports((session?.user.roles ?? []) as UserRole[]);
  const defaultSport = roleSports.length === 1 ? roleSports[0] : undefined;

  const [sport, setSport] = useState<Sport | undefined>(defaultSport);
  const [permission, setPermission] = useState<VideoPermission>();
  const [metadata, setMetadata] = useState<string[]>([]);
  const [athletes, setAthletes] = useState<VideoUpdateInput["athletes"]>([]);
  const [competition, setCompetition] = useState<Competition>();
  const [files, setFiles] = useState<(MultiUploadFile | null)[]>([]);

  const { mutate: updateMany, isPending } = api.video.updateMany.useMutation({
    onSuccess: () => {
      toast.success("Videos updated successfully");
    },
    onError: () => {
      toast.error("Failed to update videos");
    },
  });

  const hasUnCompletedVideos = files.some(
    (x) => x !== null && !x.muxPlaybackId,
  );

  usePreventUnload(hasUnCompletedVideos);

  const onFilesSelect = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.files;
    if (!value || !permission || !sport) return;
    setFiles([
      ...files, //files has to be first to avoid re-rendering
      ...Array.from(value).map((item) => ({
        file: item,
        title: item.name,
        permission,
        sport,
        status: VideoStatus.Raw,
        key: item.name + Date.now(),
        metadata,
        videoDate: new Date(),
        athletes,
        competition,
      })),
    ]);
  };

  const onFileRemove = (index: number) => {
    const newFiles = [...files];
    newFiles[index] = null;
    setFiles(newFiles);
  };

  const onVideoEdit = (index: number, updatedVideo: MultiUploadFile) => {
    const newFiles = [...files];
    newFiles[index] = updatedVideo;
    setFiles(newFiles);
  };

  const handleSubmit = () => {
    const activeFiles = files.filter((x) => x !== null) as MultiUploadFile[];
    updateMany(
      activeFiles
        .filter((x) => !!x.id)
        .map((x) => ({
          id: x.id!,
          title: x.title,
          sport: x.sport,
          permission: x.permission,
          competition: x.competition,
          athletes: x.athletes ?? [],
          tags: x.metadata ?? [],
          videoDate: x.videoDate,
        })),
    );
  };

  const uploadDisabled = !sport || !permission;

  return (
    <>
      <div className="grid w-full grid-cols-2 gap-8 lg:grid-cols-3">
        <div className="col-span-3 w-full gap-4 rounded-3xl border border-white/30 p-4">
          <div className="flex justify-between">
            <p>Multi-Upload</p>
            <Label
              htmlFor="upload"
              className={cn(
                "rounded-full bg-white px-4 py-1 text-black hover:brightness-90",
                uploadDisabled ? " cursor-not-allowed" : "cursor-pointer",
              )}
            >
              Upload Video
            </Label>
            <input
              hidden
              id="upload"
              disabled={uploadDisabled}
              type="file"
              multiple
              accept="video/*"
              onChange={onFilesSelect}
            />
          </div>
          <div className="mt-4 grid gap-4 md:grid-cols-5">
            <div>
              <Label className="mb-2 block">SPORT</Label>
              <Select
                value={sport}
                onValueChange={(value: Sport) => {
                  setSport(value);
                  setMetadata([]);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a sport..." />
                </SelectTrigger>
                <SelectContent>
                  {roleSports.map((item) => (
                    <SelectItem value={item} key={item}>
                      <div className="capitalize">
                        {item.replaceAll("_", " ")}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="mb-2 block">PERMISSION</Label>
              <Select
                value={permission}
                onValueChange={(value: VideoPermission) => setPermission(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a permission..." />
                </SelectTrigger>
                <SelectContent>
                  {videoPermissions.map((item) => (
                    <SelectItem value={item} key={item}>
                      <div className="capitalize">
                        {item.replaceAll("_", " ")}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {sport && (
              <CompetitionSelect
                label="COMPETITION"
                setSelectedCompetition={(value) => {
                  setCompetition(value);
                }}
                selectedCompetition={competition}
                sport={sport}
              />
            )}
            {sport && (
              <Metadata
                sport={sport}
                selectedTags={metadata}
                onTagClick={(tag: string) => {
                  setMetadata((prev) => {
                    if (prev.includes(tag)) {
                      return prev.filter((x) => x !== tag);
                    }
                    return [...prev, tag];
                  });
                }}
              />
            )}
            {sport && (
              <AthleteSelect
                label="ATHLETES"
                selectedAthletes={athletes}
                setSelectedAthletes={(value) => {
                  setAthletes(value);
                }}
                sport={sport}
              />
            )}
          </div>
        </div>
        <div className="col-span-full grid gap-4">
          {files.map((item, index) => (
            <Fragment key={index}>
              {item && (
                <>
                  <AccountVideoUploadItem
                    video={item}
                    roleSports={roleSports}
                    removeItem={() => onFileRemove(index)}
                    onUpdate={(video) => onVideoEdit(index, video)}
                  />
                  {files.length > 1 && (
                    <div className="py-8 md:py-6 lg:hidden">
                      <Divider />
                    </div>
                  )}
                </>
              )}
              {!item && <div key={index} />}
            </Fragment>
          ))}
        </div>
        {files.length > 0 && (
          <Button
            onClick={handleSubmit}
            variant="primary"
            loading={isPending}
            className="col-span-2 mt-4 w-full lg:mt-8 lg:w-fit lg:px-6"
          >
            Submit
          </Button>
        )}
      </div>
    </>
  );
};
