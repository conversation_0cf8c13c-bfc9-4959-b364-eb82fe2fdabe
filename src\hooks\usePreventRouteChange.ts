import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";

export function usePreventUnload(shouldPrevent: boolean) {
  useEffect(() => {
    const handleBeforeUnload = (e: {
      preventDefault: () => void;
      returnValue: string;
    }) => {
      if (shouldPrevent) {
        e.preventDefault();
        e.returnValue = "warning";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [shouldPrevent]);
}

//todo: this is not working, find a proper way to prevent route change
export function usePreventRouteChange(shouldPrevent: boolean) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    alert(pathname);
    if (shouldPrevent) {
      const handleBeforeRouteChange = () => {
        if (!window.confirm("Are you sure you want to leave this page?")) {
          window.history.pushState(null, "", pathname);
        }
      };

      window.addEventListener("popstate", handleBeforeRouteChange);

      return () => {
        window.removeEventListener("popstate", handleBeforeRouteChange);
      };
    }
  }, [shouldPrevent, pathname, searchParams]);
}
