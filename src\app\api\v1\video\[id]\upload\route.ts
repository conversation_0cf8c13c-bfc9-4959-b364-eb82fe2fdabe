import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import Mux from "@mux/mux-node";
import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { env } from "~/env";
import { s3Client } from "~/server/api/routers/aws";
import { db } from "~/server/db";
import { videos } from "~/server/db/schema";
import { convertVideo } from "~/server/utils/aws";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { refreshMUXToken } from "~/server/utils/mux";
import { checkToken } from "~/server/utils/permissions";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/upload:
 *   put:
 *     description: |
 *       Complete upload a video. Call this endpoint after video is uploaded to s3 bucket.
 *
 *       **Roles required:** api, admin / analyst
 *     tags: [Upload]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *       200:
 *         description: Video summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 title:
 *                   type: string
 *                 thumbnail:
 *                   type: string
 *                 sport:
 *                   type: string
 *                 permission:
 *                   type: string
 *                 isDraft:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 status:
 *                   type: string
 *                 competitionId:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                 updatedAt:
 *                   type: string
 */

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;
  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "No video ID",
    });
  }

  try {
    const { tokenPayload, token } = await checkToken(request);

    const video = await getVideoByIdForPermissionCheck(
      id,
      tokenPayload.roles,
      token,
    );

    if (!video?.fullFilename) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }

    const getObjectCommand = new GetObjectCommand({
      Bucket: env.AWS_BUCKET_NAME,
      Key: video.fullFilename,
    });

    const videoPresignedUrl = await getSignedUrl(s3Client, getObjectCommand, {
      expiresIn: 3600 * 24, // URL expires in 1 day
    });

    const mux = new Mux({
      tokenId: env.MUX_ACCESS_TOKEN_ID,
      tokenSecret: env.MUX_SECRET_KEY,
    });
    const asset = await mux.video.assets.create({
      input: [{ url: videoPresignedUrl }],
      playback_policy: ["signed"],
      max_resolution_tier: "2160p",
    });
    const muxPlaybackId = asset.playback_ids?.[0]?.id;

    const videoTrack = asset.tracks?.find((track) => track.type === "video");

    if (!muxPlaybackId) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to create MUX asset",
      });
    }

    const [
      { newToken: thumbnailToken },
      { newToken: playbackToken },
      { newToken: storyboardToken },
    ] = await Promise.all([
      await refreshMUXToken({
        aud: "thumbnail",
        muxPlaybackId,
      }),
      await refreshMUXToken({
        aud: "video",
        muxPlaybackId,
      }),
      await refreshMUXToken({
        aud: "storyboard",
        muxPlaybackId,
      }),
    ]);

    const updatedVideo = await db.transaction(async (tx) => {
      await tx
        .update(videos)
        .set({
          muxAssetId: asset.id,
          muxPlaybackId,
          thumbnailToken,
          playbackToken,
          storyboardToken,
          isDraft: false,
          duration: videoTrack?.duration,
          maxHeight: videoTrack?.max_height,
          maxWidth: videoTrack?.max_width,
        })
        .where(eq(videos.id, id));

      return await tx.query.videos.findFirst({
        where: (fields) => eq(fields.id, id),
        columns: {
          id: true,
          title: true,
          thumbnail: true,
          sport: true,
          permission: true,
          isDraft: true,
          isDeleted: true,
          status: true,
          competitionId: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    });

    if (env.ENABLE_VIDEO_CONVERTER === "true") {
      //make m3u8
      await convertVideo({
        videoId: id,
        fullFilename: video.fullFilename,
      });
    }

    return Response.json(updatedVideo);
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
