"use client";

import { Search, Upload } from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { SortBy, UserRole, sortBy, videoStatuses } from "~/lib/enums";
import { getUserSports } from "~/lib/roles";
import { VideoFilter } from "./VideoFilter";

export const Filters = () => {
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const isAnalystOrAdmin =
    !!session?.user.roles.includes(UserRole.admin) ||
    !!session?.user.roles.includes(UserRole.analyst);

  const selectedSport = searchParams.get("sports") ?? "";

  const router = useRouter();
  const pathname = usePathname();
  const [searchInput, setSearchText] = useState(
    searchParams.get("searchText") ?? "",
  );

  const userSports = getUserSports((session?.user.roles ?? []) as UserRole[]);
  const routesWithFilters = ["/videos", "/account/videos"];
  const onlySport = userSports.length === 1 ? userSports[0] : null;

  useEffect(() => {
    if (onlySport) {
      const params = new URLSearchParams(searchParams.toString());
      params.set("sports", onlySport);
      router.replace(`${pathname}?${params.toString()}`);
    }
  }, [onlySport, searchParams, pathname, router]);

  if (!routesWithFilters.includes(pathname)) {
    return null;
  }

  return (
    <div className="flex-col md:flex md:flex-row md:items-center md:justify-center md:gap-4">
      <div className="hidden md:block">
        <Button
          variant="ghost"
          className="rounded-full p-3 text-white hover:bg-slate-200 hover:bg-opacity-30"
        >
          <Link href="/account/videos/upload">
            <Upload className=" h-5 w-5 cursor-pointer text-white" />
          </Link>
        </Button>
      </div>
      <div className="relative">
        <Input
          placeholder="Search..."
          value={searchInput}
          onChange={(event) => setSearchText(event.target.value)}
          onFinish={() => {
            const params = new URLSearchParams(searchParams.toString());
            params.set("searchText", searchInput);
            params.set("page", "1");
            router.push(`${pathname}?${params.toString()}`);
          }}
          className="rounded-full border border-white bg-transparent px-6 md:max-w-sm"
        />
        <Search className="absolute right-3 top-3 h-4 w-4" />
      </div>

      <div className="flex flex-wrap justify-start md:justify-between md:gap-4">
        <VideoFilter filterEnumValues={userSports} filterKey="sports" />
        {selectedSport && (
          <VideoFilter filterKey="metadata" filterEnumValues={[]} />
        )}
        {isAnalystOrAdmin && (
          <VideoFilter filterEnumValues={videoStatuses} filterKey="status" />
        )}
        <VideoFilter
          filterKey="sort_by"
          filterEnumValues={sortBy}
          defaultValue={[SortBy.video_date]}
        />
      </div>
    </div>
  );
};
