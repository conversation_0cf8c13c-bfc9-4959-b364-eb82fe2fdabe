import { AccountVideoList } from "~/app/_components/account/VideoList";
import type { SortBy, Sport, VideoPermission, VideoStatus } from "~/lib/enums";
import { getServerAuthSession } from "~/server/auth";
import { api } from "~/trpc/server";

//force update if there are data changes in db
export const dynamic = "force-dynamic";

export default async function AccountVideosPage({
  searchParams,
}: {
  searchParams: {
    page?: number;
    pageSize?: number;
    permission?: VideoPermission[] | VideoPermission;
    published?: string[] | string;
    sports?: Sport[] | Sport;
    status?: VideoStatus[] | VideoStatus;
    searchText?: string;
    metadata?: string[];
    sort_by?: SortBy;
  };
}) {
  const page = searchParams.page ?? 1;
  const pageSize = searchParams.pageSize ?? 10;
  const { status, sports, published, permission, searchText, sort_by } =
    searchParams;

  const metadata =
    typeof searchParams.metadata === "string"
      ? [searchParams.metadata]
      : searchParams.metadata;

  let isDraft = undefined;
  if (published === "false") {
    isDraft = true;
  } else if (published === "true") {
    isDraft = false;
  }

  const videos = await api.account.videos({
    page: page - 1,
    pageSize,
    searchText,
    filter: {
      status: typeof status === "string" ? [status] : status,
      sport: typeof sports === "string" ? [sports] : sports,
      permission: typeof permission === "string" ? [permission] : permission,
      isDraft,
      metadata,
      sortBy: sort_by,
    },
  });

  const session = await getServerAuthSession();

  return <AccountVideoList videos={videos} session={session} />;
}
