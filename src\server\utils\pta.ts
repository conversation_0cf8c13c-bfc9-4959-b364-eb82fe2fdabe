import { TRPCError } from "@trpc/server";
import { env } from "~/env";
import type { Sport } from "~/lib/enums";
import type { Athletes, MetadataNode, PTACompetition } from "~/lib/interface";
import type { Context } from "../api/trpc";
import { unstable_cache } from "next/cache";
import { getServiceToken } from "./keycloak";
// import { getToken } from "next-auth/jwt";

export const getAccessToken = async (ctx: Context) => {
  if (!ctx.session?.accessToken) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "User is not authenticated",
    });
  }
  return ctx.session?.accessToken;
  // if (!ctx.req) {
  //   throw new TRPCError({
  //     code: "UNAUTHORIZED",
  //     message: "User is not authenticated",
  //   });
  // }

  // const token = await getToken({ req: ctx.req });
  // if (!token?.access_token) {
  //   throw new TRPCError({
  //     code: "UNAUTHORIZED",
  //     message: "User is not authenticated",
  //   });
  // }
  // return token.access_token as string;
};

export const getAthletes = async ({
  token,
  name,
  sport,
  is_hp,
  athlete_ids,
}: {
  token: string;
  name?: string;
  sport?: string;
  is_hp?: boolean;
  athlete_ids?: string;
}) => {
  const params = new URLSearchParams();

  if (sport) {
    params.set("sport", sport);
  }
  if (name) {
    if (name.length < 3) return;
    params.set("name", name);
  }
  if (is_hp) {
    params.set("is_hp", is_hp.toString());
  }
  if (athlete_ids) {
    params.set("athlete_ids", athlete_ids);
  }
  params.set("active", "true");
  const res = await fetch(
    `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/athletes?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  if (!res.ok) {
    console.error("Failed to fetch athletes", res.statusText);
    return [];
  }

  const json = (await res.json()) as Athletes[];

  return json;
};

export const getAthletesAttr = async (
  athletes: {
    athleteId: string;
    name?: string;
    isHp?: boolean;
    videoId: string;
  }[],
) => {
  if (athletes.length === 0) return [];
  const serviceToken = await getServiceToken();
  const token = serviceToken.access_token;
  const uniqueAthleteIds = [...new Set(athletes.map((x) => x.athleteId))];
  const athletesWithAttr =
    (await getAthletes({
      token,
      athlete_ids: uniqueAthleteIds.join(","),
    })) ?? [];
  const athletesWithAttrMap = athletes.map((athlete) => {
    const athleteWithAttr = athletesWithAttr.find(
      (x) => x.athlete_id === athlete.athleteId,
    );
    return {
      ...athlete,
      name: athleteWithAttr
        ? athleteWithAttr.first_name + " " + athleteWithAttr.last_name
        : "unknown",
      isHp: athleteWithAttr?.is_hp,
    };
  });

  return athletesWithAttrMap;
};

export const getHpAthleteCached = unstable_cache(
  async () => {
    const serviceToken = await getServiceToken();
    const token = serviceToken.access_token;
    const athletes = await getAthletes({
      token,
      is_hp: true,
    });
    if (!athletes) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch HP athletes",
      });
    }
    return athletes;
  },
  ["hp-athletes"],
  {
    revalidate: 55 * 60, // 55 minutes
    // revalidate: 5,
  },
);

export const getAthletesByIds = async ({
  token,
  ids,
}: {
  token: string;
  ids: string[];
}) => {
  const params = new URLSearchParams();
  params.append("athlete_ids", ids.join(","));

  const res = await fetch(
    `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/athletes?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const json = (await res.json()) as Athletes[];

  return json;
};
export const getSportMetadata = async ({
  token,
  sport,
}: {
  token: string;
  sport: Sport;
}) => {
  const upperCaseSport = sport.toUpperCase();
  try {
    const res = await fetch(
      `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/video/meta-tags`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (res.ok) {
      const json = (await res.json()) as MetadataNode;

      const tags: Record<string, string[]> = {};

      Object.entries(json).forEach(([categoryKey, category]) => {
        Object.entries(category.attributes).forEach(([key, value]) => {
          if (categoryKey !== "SPORT") {
            const attr: string[] = [];
            Object.entries(value.attributes).forEach(([_, v]) => {
              if (v.active) {
                attr.push(v.display_name);
              }
            });
            tags[key] = attr;
          } else {
            //sport is one more level deep
            const sportMetaTags = category.attributes[upperCaseSport];
            if (!sportMetaTags) return;
            const attributes = sportMetaTags.attributes;
            Object.entries(attributes).forEach(([sportKey, sportValue]) => {
              if (sportValue.active) {
                const attr: string[] = [];
                Object.entries(sportValue.attributes).forEach(([_, v]) => {
                  if (v.active) {
                    attr.push(v.display_name);
                  }
                });
                tags[sportKey] = attr;
              }
            });
          }
        });
      });

      return tags;
    } else {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch metadata",
      });
    }
  } catch (error) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch metadata",
    });
  }
};

export const getCompetitions = async ({
  token,
  sport,
}: {
  token: string;
  sport: string;
}) => {
  // const capitalSport = sport.charAt(0).toUpperCase() + sport.slice(1);
  try {
    const res = await fetch(
      `${env.NEXT_PUBLIC_PTA_ROOT_URL}/api/v2/competitions?sport=${sport}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    const json = (await res.json()) as PTACompetition[];
    return json;
  } catch (error) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch competitions",
    });
  }
};
