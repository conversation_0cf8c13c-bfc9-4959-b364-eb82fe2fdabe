"use client";

import MuxPlayer from "@mux/mux-player-react";

export const MUXPlayer = ({
  thumbnailToken,
  playbackToken,
  storyboardToken = "",
  playbackId,
  onPause,
}: {
  playbackToken: string;
  thumbnailToken: string;
  storyboardToken?: string;
  playbackId: string;
  onPause?: (time: number) => void;
}) => {
  return (
    <div className="flex w-full items-center justify-center">
      {playbackToken && (
        <MuxPlayer
          streamType="on-demand"
          accentColor="#fd8c40"
          playbackId={playbackId}
          tokens={{
            playback: playbackToken,
            thumbnail: thumbnailToken,
            storyboard: storyboardToken,
          }}
          className="aspect-video h-full w-full rounded-2xl"
          playbackRates={[0.25, 0.5, 0.75, 1, 1.2, 1.5, 1.7, 2]}
          disableCookies
          disableTracking
          onSeeked={(e) => {
            if (onPause) {
              const target = e.target as unknown as { currentTime: number };
              const currentTime = target.currentTime;
              onPause(currentTime);
            }
          }}
          onPause={(e) => {
            if (onPause) {
              const target = e.target as unknown as { currentTime: number };
              const currentTime = target.currentTime;
              onPause(currentTime);
            }
          }}
        />
      )}
    </div>
  );
};
