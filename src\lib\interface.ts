import type {
  SortBy,
  Sport,
  UserRole,
  VideoPermission,
  VideoStatus,
} from "./enums";

export interface DecodedToken {
  exp: number;
  sub: string;
  typ: string;
  azp: string;
  kid: string;
  email_verified: boolean;
  aud: string[];
  roles: UserRole[];
  name: string;
  preferred_username: string;
  given_name: string;
  family_name: string;
  email: string;
}

export interface RequestTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  "not-before-policy": number;
  session_state: string;
  scope: string;
}

export interface Athletes {
  athlete_id: string;
  first_name: string;
  last_name: string;
  gender: "Men" | "Women";
  dob: string;
  country: string;
  sport: string;
  active: boolean;
  is_hp: boolean;
}

export interface DBAthleteWithName {
  athleteId: string;
  name: string;
  isHp: boolean;
  videoId: string;
}

export interface PTACompetition {
  competition_id: string;
  competition_name: string;
  competition_type?: string;
  start_date?: string;
  location?: string;
  sport: string;
  is_official?: boolean;
}

export interface MuxVideoFile {
  muxAssetId: string;
  muxPlaybackId: string;
  fullFilename: string;
  filename: string;
  duration: number;
  maxHeight: number;
  maxWidth: number;
  thumbnailToken: string;
  playbackToken: string;
  storyboardToken: string;
}

export type MetadataNode = Record<
  string,
  {
    display_name: string;
    active: boolean;
    attributes: MetadataNode;
  }
>;

export interface VideoListSearchParams {
  page?: number;
  pageSize?: number;
  searchText?: string;
  sports?: Sport[];
  status?: VideoStatus[];
  metadata?: string[];
  sort_by?: SortBy;
}

export interface ThumnailParams {
  time?: number;
  width?: number;
  height?: number;
  rotate?: 90 | 180 | 270;
  fit_mode?: "preserve" | "stretch" | "crop" | "smartcrop" | "pad";
}

export interface VideoSummary {
  id: string;
  title: string;
  thumbnail: string;
  sport: Sport;
  permission: VideoPermission;
  status: VideoStatus;
  isDraft: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface VideoPart {
  PartNumber: number;
  ETag: string;
}
