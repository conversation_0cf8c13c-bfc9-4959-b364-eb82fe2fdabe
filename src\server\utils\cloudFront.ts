import { getSignedUrl } from "@aws-sdk/cloudfront-signer";
import { env } from "~/env";

export function getCloudFrontSignedUrl(key: string): string {
  const url = `${env.CLOUDFRONT_DOMAIN}/${key}`;
  const keyPairId = env.CLOUDFRONT_KEY_PAIR_ID;
  const privateKey = env.CLOUDFRONT_PRIVATE_KEY;

  const signedUrl = getSignedUrl({
    url,
    keyPairId,
    dateLessThan: new Date(Date.now() + 60 * 60 * 1000 * 24).toISOString(), // URL expires in 1 day
    privateKey,
  });

  return signedUrl;
}
