"use client";

import { <PERSON>u } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetTrigger } from "~/components/ui/sheet";
import Link from "next/link";
import { Button } from "~/components/ui/button";
import { SignoutButton } from "./SignoutButton";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { cn, getAvatarLettersByUsername } from "~/lib/utils";
import { UserRole } from "~/lib/enums";
import { env } from "~/env";
import { useState } from "react";
import { useSession } from "next-auth/react";

interface ItemProp {
  adminItem?: boolean;
  href: string;
  label: string;
  target?: string;
}

const adminItems = [
  {
    adminItem: true,
    href: `${env.NEXT_PUBLIC_KEYCLOAK_ROOT}/admin/hpsnz/console/#/hpsnz/users`,
    label: "Manage Users",
    target: "__blank",
  },
  {
    adminItem: true,
    href: `${env.NEXT_PUBLIC_PTA_ROOT_URL}/account/permissions/`,
    label: "Add/Remove Users",
    target: "__blank",
  },
];

export const MobileSidebar = () => {
  const { data: session } = useSession();
  const isAdmin = session?.user.roles.includes(UserRole.admin);
  const hasApiPermission =
    !!isAdmin || session?.user.roles.includes(UserRole.api);

  const baseItems = [
    {
      href: "/",
      label: "Home",
    },
    {
      href: "/account/videos/upload",
      label: "Upload Video",
    },
    {
      href: "/account/videos",
      label: "Manage videos",
    },
    {
      href: "https://gm-auth-dev.azurewebsites.net/realms/hpsnz/account",
      label: "Manage My Account",
      target: "__blank",
    },
  ];

  if (hasApiPermission) {
    baseItems.push({
      href: "/api-doc",
      label: "API doc",
      target: "__blank",
    });
  }

  const items: ItemProp[] = isAdmin ? [...baseItems, ...adminItems] : baseItems;

  const [open, setOpen] = useState(false);

  const onClose = () => {
    setOpen(false);
  };

  return (
    <Sheet open={open} onOpenChange={(open) => setOpen(open)}>
      <SheetTrigger
        className="pr-4 transition hover:opacity-75 md:hidden"
        onClick={() => {
          setOpen(true);
        }}
      >
        <Menu />
      </SheetTrigger>
      <SheetContent side="left" className="w-full border bg-black p-0">
        <div className="flex px-4 py-8">
          <Avatar>
            <AvatarImage src={session?.user.image ?? ""} />
            <AvatarFallback className="text-black">
              {getAvatarLettersByUsername(session?.user.name)}
            </AvatarFallback>
          </Avatar>
          <div className=" px-4 text-white">
            <p className="text-lg capitalize">{session?.user.name}</p>
            <p className="truncate text-xs text-stone-500">
              {session?.user.email}
            </p>
          </div>
        </div>
        <div className="mt-4 flex flex-col gap-4 px-4">
          {items.map(({ href, target, adminItem, label }) => (
            <Link key={href} href={href} target={target}>
              <Button
                variant="default"
                className={cn(
                  "mx-auto w-full",
                  adminItem ? "bg-orange text-white" : "bg-white",
                )}
                onClick={onClose}
              >
                {label}
              </Button>
            </Link>
          ))}
          <SignoutButton />
        </div>
      </SheetContent>
    </Sheet>
  );
};
