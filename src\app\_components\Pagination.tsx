import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "~/components/ui/pagination";
import type { VideoListSearchParams } from "~/lib/interface";
import { getUrlSearchParams } from "~/lib/utils";

export const PaginationList = ({
  itemsCount,
  searchParams,
}: {
  itemsCount: number;
  searchParams: VideoListSearchParams;
}) => {
  const page = +(searchParams.page ?? 1);
  const pageSize = +(searchParams.pageSize ?? 12);
  const totalPageCount = Math.max(1, Math.ceil(itemsCount / pageSize));

  const prevParams = getUrlSearchParams({
    ...(searchParams as Record<string, string | number>),
    page: page - 1,
  });

  const nextParams = getUrlSearchParams({
    ...(searchParams as Record<string, string | number>),
    page: page + 1,
  });

  let middlePages: number[] = [];
  if (page === 1) {
    middlePages = [2, 3];
  } else if (page === totalPageCount) {
    middlePages = [totalPageCount - 2, totalPageCount - 1];
  } else {
    middlePages = [page - 1, page, page + 1];
  }

  return (
    <div className="flex w-full items-center justify-center">
      <Pagination>
        <PaginationContent>
          {page > 1 && (
            <PaginationItem>
              <PaginationPrevious href={`/videos?${prevParams}`} />
            </PaginationItem>
          )}
          {page !== 2 && (
            <PaginationItem>
              <PaginationLink
                href={`/videos?${getUrlSearchParams({ ...(searchParams as Record<string, string | number>), page: 1 })}`}
                isActive={page === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>
          )}

          {middlePages[0] && middlePages[0] > 2 && (
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          )}

          {totalPageCount >= 4 &&
            middlePages.map((pageNumber) => (
              <PaginationItem key={pageNumber}>
                <PaginationLink
                  href={`/videos?${getUrlSearchParams({ ...(searchParams as Record<string, string | number>), page: pageNumber })}`}
                  isActive={page === pageNumber}
                >
                  {pageNumber}
                </PaginationLink>
              </PaginationItem>
            ))}
          {middlePages[middlePages.length - 1]! < totalPageCount - 1 && (
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          )}
          {totalPageCount > 1 && page !== totalPageCount - 1 && (
            <PaginationItem>
              <PaginationLink
                href={`/videos?${getUrlSearchParams({ ...(searchParams as Record<string, string | number>), page: totalPageCount })}`}
                isActive={page === totalPageCount}
              >
                {totalPageCount}
              </PaginationLink>
            </PaginationItem>
          )}
          {page < totalPageCount && (
            <PaginationItem>
              <PaginationNext href={`/videos?${nextParams}`} />
            </PaginationItem>
          )}
        </PaginationContent>
      </Pagination>
    </div>
  );
};
