import { type InferSelectModel, relations, sql } from "drizzle-orm";
import {
  boolean,
  float,
  index,
  int,
  mysqlEnum,
  mysqlTableCreator,
  primaryKey,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { type AdapterAccount } from "next-auth/adapters";
import {
  VideoPermission,
  VideoStatus,
  sports,
  videoPermissions,
  videoStatuses,
} from "~/lib/enums";

/**
 * This is an example of how to use the multi-project schema feature of Drizzle ORM. Use the same
 * database instance for multiple projects.
 *
 * @see https://orm.drizzle.team/docs/goodies#multi-project-schema
 */
export const createTable = mysqlTableCreator((name) => `hpsnz_${name}`);

export const users = createTable("user", {
  id: varchar("id", { length: 255 }).notNull().primaryKey(),
  name: varchar("name", { length: 255 }),
  email: varchar("email", { length: 255 }).notNull(),
  emailVerified: timestamp("emailVerified", {
    mode: "date",
    fsp: 3,
  }).default(sql`CURRENT_TIMESTAMP(3)`),
  image: varchar("image", { length: 255 }),
});

export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  videos: many(videos),
}));

export const accounts = createTable(
  "account",
  {
    userId: varchar("userId", { length: 255 }).notNull(),
    type: varchar("type", { length: 255 })
      .$type<AdapterAccount["type"]>()
      .notNull(),
    provider: varchar("provider", { length: 255 }).notNull(),
    providerAccountId: varchar("providerAccountId", { length: 255 }).notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: int("expires_at"),
    token_type: varchar("token_type", { length: 255 }),
    scope: varchar("scope", { length: 255 }),
    id_token: text("id_token"),
    session_state: varchar("session_state", { length: 255 }),
  },
  (account) => ({
    compoundKey: primaryKey({
      columns: [account.provider, account.providerAccountId],
    }),
    userIdIdx: index("account_userId_idx").on(account.userId),
  }),
);

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, { fields: [accounts.userId], references: [users.id] }),
}));

export const sessions = createTable(
  "session",
  {
    sessionToken: varchar("sessionToken", { length: 255 })
      .notNull()
      .primaryKey(),
    userId: varchar("userId", { length: 255 }).notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (session) => ({
    userIdIdx: index("session_userId_idx").on(session.userId),
  }),
);

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, { fields: [sessions.userId], references: [users.id] }),
}));

export const verificationTokens = createTable(
  "verificationToken",
  {
    identifier: varchar("identifier", { length: 255 }).notNull(),
    token: varchar("token", { length: 255 }).notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (vt) => ({
    compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
  }),
);

export const videos = createTable("video", {
  id: varchar("id", { length: 128 }).primaryKey(),
  filename: varchar("filename", { length: 255 }),
  fullFilename: varchar("fullFilename", { length: 255 }), //s3 object full name, = {filename}_{datetime}
  muxAssetId: varchar("muxAssetId", { length: 128 }),
  muxPlaybackId: varchar("muxPlaybackId", { length: 128 }),
  title: varchar("title", { length: 255 }),
  thumbnail: varchar("thumbnail", { length: 255 }),
  thumbnailToken: text("thumbnailToken"),
  playbackToken: text("playbackToken"),
  storyboardToken: text("storyboardToken"),
  sport: mysqlEnum("sport", sports),
  permission: mysqlEnum("permission", videoPermissions)
    .notNull()
    .default(VideoPermission.public),
  videoDate: timestamp("videoDate").default(sql`CURRENT_TIMESTAMP`),
  duration: float("duration").default(0),
  maxHeight: int("maxHeight").default(0),
  maxWidth: int("maxWidth").default(0),
  isDraft: boolean("isDraft").default(true),
  isDeleted: boolean("isDeleted").default(false),
  status: mysqlEnum("status", videoStatuses).default(VideoStatus.Raw),
  competitionId: varchar("competitionId", { length: 128 }),
  snowSportsRaceId: varchar("snowSportsRaceId", { length: 128 }),
  fps: int("fps"),
  parentId: varchar("parentId", { length: 128 }),
  relationshipType: varchar("relationshipType", { length: 64 }),
  relationshipCreatedDate: timestamp("relationshipCreatedDate").onUpdateNow(),
  syncOffset: int("syncOffset"),
  createdById: varchar("createdById", { length: 255 }),
  createdAt: timestamp("created_at")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp("updatedAt").onUpdateNow(),
});

export type Video = InferSelectModel<typeof videos>;

export const videoRelations = relations(videos, ({ one, many }) => ({
  user: one(users, { fields: [videos.createdById], references: [users.id] }),
  competition: one(competition, {
    fields: [videos.competitionId],
    references: [competition.id],
  }),
  athletes: many(videoAthletes),
  tags: many(videoTags),
  parent: one(videos, {
    fields: [videos.parentId],
    references: [videos.id],
    relationName: "videoHierarchy",
  }),
  children: many(videos, { relationName: "videoHierarchy" }),
}));

export const videoAthletes = createTable(
  "videoAthletes",
  {
    athleteId: varchar("athleteId", { length: 255 }).notNull(),
    videoId: varchar("videoId", { length: 255 }).notNull(),
  },
  (athlete) => ({
    compoundKey: primaryKey({
      columns: [athlete.athleteId, athlete.videoId],
    }),
    athleteVideoIdIdx: index("athlete_videoId_idx").on(athlete.videoId),
  }),
);

export type VideoAthlete = InferSelectModel<typeof videoAthletes>;

export const videoAthletesRelations = relations(videoAthletes, ({ one }) => ({
  video: one(videos, {
    fields: [videoAthletes.videoId],
    references: [videos.id],
  }),
}));

export const videoTags = createTable(
  "videoTags",
  {
    text: varchar("text", { length: 64 }).notNull(),
    videoId: varchar("videoId", { length: 255 }).notNull(),
  },
  (tag) => ({
    compoundKey: primaryKey({
      columns: [tag.text, tag.videoId],
    }),
  }),
);

export type VideoTag = InferSelectModel<typeof videoTags>;

export const videoTagsRelations = relations(videoTags, ({ one }) => ({
  video: one(videos, {
    fields: [videoTags.videoId],
    references: [videos.id],
  }),
}));

export const competition = createTable("competition", {
  id: varchar("id", { length: 128 }).primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  type: varchar("type", { length: 255 }),
  date: varchar("date", { length: 64 }),
  location: varchar("location", { length: 255 }),
  sport: mysqlEnum("sport", sports).notNull(),
  isOfficial: boolean("isOfficial").default(false),
});

export type VideoCompetition = InferSelectModel<typeof competition>;

export const competitionRelations = relations(competition, ({ many }) => ({
  videos: many(videos),
}));
