import { TRPCError } from "@trpc/server";
import { and, eq, inArray } from "drizzle-orm";
import type { NextRequest } from "next/server";
import { UserRole } from "~/lib/enums";
import { db } from "~/server/db";
import { videoTags } from "~/server/db/schema";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";
import { getSportMetadata } from "~/server/utils/pta";
import { getVideoByIdForPermissionCheck } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video/{id}/tags:
 *   get:
 *     description: Get video tags.
 *     tags: [Get/list video(s)]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     responses:
 *        200:
 *          description: Video tags
 *          content:
 *            application/json:
 *              schema:
 *                type: array
 *                items:
 *                  type: object
 *                  properties:
 *                    text:
 *                      type: string
 *   put:
 *     description: |
 *       Update tags to a video. Tags must match video sport metadata.
 *
 *       **Roles required:** api, api_modify, admin / analyst
 *     tags: [Update video]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The video ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               add:
 *                 type: array
 *                 items:
 *                   type: string
 *               remove:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Video tags
 *         content:
 *           application/json:
 *             schema:
 *                type: array
 *                items:
 *                  type: object
 *                  properties:
 *                    text:
 *                      type: string
 */

interface UpdateTag {
  add?: string[];
  remove?: string[];
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const id = params.id;
  if (!id) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }
  try {
    const { tokenPayload, token } = await checkToken(request);
    await getVideoByIdForPermissionCheck(id, tokenPayload.roles, token);

    if (!id) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Invalid video id",
      });
    }

    const tags = await db.query.videoTags.findMany({
      where: (fields) => eq(fields.videoId, id),
      columns: {
        text: true,
      },
    });
    return Response.json(tags);
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  const videoId = params.id;

  const body = (await request.json()) as UpdateTag;

  if (!videoId) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid video id",
    });
  }

  try {
    const { tokenPayload, token } = await checkToken(request, [
      UserRole.api_modify,
    ]);
    const video = await getVideoByIdForPermissionCheck(
      videoId,
      tokenPayload.roles,
      token,
    );

    const sport = video?.sport;
    if (!sport) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Video not found",
      });
    }
    const metadataPool = await getSportMetadata({
      token,
      sport,
    });
    const metadataPoolArray: string[] = [];

    Object.entries(metadataPool).forEach(([_, values]) => {
      metadataPoolArray.push(...values);
    });

    if (body.add && body.add.some((x) => !metadataPoolArray.includes(x))) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Invalid tag",
      });
    }

    const tags = await db.transaction(async (tx) => {
      if (body.remove && body.remove.length > 0) {
        await tx
          .delete(videoTags)
          .where(
            and(
              eq(videoTags.videoId, videoId),
              inArray(videoTags.text, body.remove),
            ),
          );
      }
      if (body.add && body.add.length > 0) {
        await tx
          .insert(videoTags)
          .values(body.add.map((tag) => ({ text: tag, videoId })));
      }
      return await tx.query.videoTags.findMany({
        where: (fields) => eq(fields.videoId, videoId),
        columns: {
          text: true,
        },
      });
    });

    return Response.json(tags);
  } catch (cause) {
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
        statusText: cause.message,
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
