import { sports, type Sport, type UserRole } from "./enums";

export const getUserSports = (roles: UserRole[]) => {
  const roleSports: Sport[] = [];
  roles?.forEach((role) => {
    if (role.includes("sport")) {
      let sport = role.split("_")[1];
      if (sport === "snowsports") {
        sport = "snow_sports";
      }
      if (sport === "canoeslalom") {
        sport = "canoe_slalom";
      }
      if (sports.includes(sport as Sport)) {
        roleSports.push(sport as Sport);
      }
    }
  });

  return roleSports.sort((a, b) => a.localeCompare(b));
};
