"use client";

import Link from "next/link";
import { cn } from "~/lib/utils";
import { usePathname } from "next/navigation";

const routes = [
  {
    label: "Videos",
    href: "/videos",
  },
];

export const NavbarRoutes = () => {
  const pathname = usePathname();
  const path = pathname.split("/")[1];

  return (
    <div className=" flex-1">
      {routes.map((route) => {
        const routePath = route.href.split("/")[1];
        return (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              "rounded-md px-2 py-1 font-bold hover:bg-primary/40 md:text-lg",
              path === routePath && "bg-primary/20",
            )}
          >
            {route.label}
          </Link>
        );
      })}
    </div>
  );
};
