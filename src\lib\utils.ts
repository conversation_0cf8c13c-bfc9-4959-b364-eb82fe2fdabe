import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import type { VideoAllOutput } from "~/server/api/routers/video";
import type { Sport } from "./enums";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getAvatarLettersByUsername(
  name: string | null | undefined,
): string {
  if (!name) return "";

  const names = name.split(" ");
  let initials = "";
  names.forEach((name) => {
    initials += name.charAt(0).toUpperCase();
  });

  return initials;
}

export async function uploadFileWithProgress(
  url: string,
  file: File | Blob,
  onProgress: (progress: number) => void,
  abortSignal?: AbortSignal,
): Promise<XMLHttpRequest> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open("PUT", url, true);
    xhr.upload.addEventListener("progress", (event) => {
      const percentComplete = (event.loaded / event.total) * 100;
      onProgress(percentComplete);
    });
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr);
      } else {
        reject(new Error(`Failed to upload file: ${xhr.statusText}`));
      }
    };
    xhr.onerror = () => {
      reject(new Error("An error occurred during the upload"));
    };

    if (abortSignal) {
      abortSignal.addEventListener("abort", () => {
        xhr.abort();
        reject(new Error("Upload aborted"));
      });

      // If the signal is already aborted, abort immediately
      if (abortSignal.aborted) {
        xhr.abort();
        reject(new Error("Upload aborted"));
        return;
      }
    }

    xhr.send(file);
  });
}

export const getVideosAthletesIds = (videos: VideoAllOutput["videoList"]) => {
  const athletesIds: string[] = [];
  videos.forEach((video) => {
    video.athletes.forEach((athlete) => {
      athletesIds.push(athlete.athleteId);
    });
  });
  return [...new Set(athletesIds)];
};

export const formatString = (str?: string | null) => {
  if (!str) return "";
  const string = str.replace(/[_\-\.]/g, " ").split(/\s+/);
  return string
    .filter((x) => x.length > 0)
    .map((x) => x.charAt(0).toUpperCase() + x.slice(1).toLowerCase())
    .join(" ");
};

export const formatSport = (sport?: Sport) => {
  if (!sport) return undefined;
  const words = sport.split("_");

  // Capitalize the first letter of each word and join with a space
  return words
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const getUrlSearchParams = (
  searchParams: Record<string, string | number>,
) => {
  const currentParams = new URLSearchParams();
  Object.entries(searchParams).forEach(([key, value]) => {
    if (value) {
      if (Array.isArray(value)) {
        value.forEach((val: string) => {
          currentParams.append(key, val);
        });
      } else {
        currentParams.append(key, value.toString());
      }
    }
  });

  return currentParams.toString();
};
