import {
  PutObjectCommand,
  CreateMultipartUploadCommand,
  UploadPartCommand,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import dayjs from "dayjs";
import { env } from "~/env";
import { s3Client } from "../api/routers/aws";
import { fileChunkSize } from "~/lib/file";
import { TRPCError } from "@trpc/server";
import { getServiceToken } from "./keycloak";

const expiresIn = 3600; // URL expires in 1 hour

export const generateSignedUrlForUpload = async ({
  filename,
}: {
  filename: string;
}) => {
  const date = dayjs().format("YYYY/MM");
  const time = Date.now();
  const fullFilename = `${date}/${time}_${filename}`;

  const putObjectCommand = new PutObjectCommand({
    Bucket: env.AWS_BUCKET_NAME,
    Key: fullFilename,
  });

  const uploadUrl = await getSignedUrl(s3Client, putObjectCommand, {
    expiresIn,
  });

  return { uploadUrl, fullFilename, expiresIn };
};

export const generateMultiuploadUrls = async ({
  fullFilename,
  filename,
  fileSize,
  chunkSize = fileChunkSize,
  uploadId,
}: {
  fullFilename?: string;
  filename: string;
  fileSize: number;
  chunkSize?: number;
  uploadId?: string;
}) => {
  if (!fullFilename) {
    const date = dayjs().format("YYYY/MM");
    const time = Date.now();
    fullFilename = `${date}/${time}_${filename}`;
  }

  const createMultipartUploadCommand = new CreateMultipartUploadCommand({
    Bucket: env.AWS_BUCKET_NAME,
    Key: fullFilename,
  });

  let UploadId = uploadId;

  if (!UploadId) {
    const res = await s3Client.send(createMultipartUploadCommand);
    UploadId = res.UploadId;
  }

  // const { UploadId } = await s3Client.send(createMultipartUploadCommand);

  if (!UploadId) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to create multipart upload",
    });
  }

  const partsCount = Math.ceil(fileSize / chunkSize);
  const signedUrls = [];

  for (let i = 1; i <= partsCount; i++) {
    const uploadPartCommand = new UploadPartCommand({
      Bucket: env.AWS_BUCKET_NAME,
      Key: fullFilename,
      UploadId,
      PartNumber: i,
    });

    const signedUrl = await getSignedUrl(s3Client, uploadPartCommand, {
      expiresIn,
    });

    signedUrls.push({ partNumber: i, signedUrl });
  }

  return {
    UploadId,
    fullFilename,
    expiresIn,
    signedUrls,
  };
};

export const convertVideo = async ({
  videoId,
  fullFilename,
  forceReplace = false,
  createSourceStream = true,
}: {
  videoId: string;
  fullFilename: string;
  forceReplace?: boolean;
  createSourceStream?: boolean;
}) => {
  const reqBody = {
    id: videoId,
    sourceKey: fullFilename,
    forceReplace,
    createSourceStream,
    sourceSettings: {
      hls_time: 4,
      x264_preset: "slow",
      x264_crf: 18,
    },
  };

  //get service token
  const tokenJson = await getServiceToken();

  const res = await fetch(env.AWS_LAMBDA_CONVERTER_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${tokenJson.access_token}`,
    },
    body: JSON.stringify(reqBody),
  });
  if (!res.ok) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to convert video",
    });
  }

  return res;
};

export async function checkIfFileExists(
  bucketName: string,
  filePath: string,
): Promise<boolean> {
  try {
    // Create a HeadObjectCommand to check if the file exists
    const headObjectParams = {
      Bucket: bucketName,
      Key: filePath,
    };

    const headCommand = new HeadObjectCommand(headObjectParams);

    // Send the command to S3
    await s3Client.send(headCommand);

    // If no error is thrown, the file exists
    return true;
  } catch (error) {
    return false;
  }
}
