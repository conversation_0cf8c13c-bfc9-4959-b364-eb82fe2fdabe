import { getApiDocs } from "~/lib/swagger";

import ReactSwagger from "./Swagger";
import { getServerAuthSession } from "~/server/auth";
import { UserRole } from "~/lib/enums";
import { ErrorBox } from "../_components/ErrorBox";
import { env } from "~/env";

export default async function IndexPage() {
  const session = await getServerAuthSession();
  const hasPermission = session?.user.roles.includes(UserRole.api);
  if (!hasPermission) {
    return (
      <div>
        <ErrorBox
          code="UNAUTHORIZED"
          message="You do not have permission to visit this page"
        />
      </div>
    );
  }

  const spec = await getApiDocs();
  const isDev = env.NODE_ENV === "development";

  return (
    <section className="container bg-white">
      {isDev && <ReactSwagger spec={spec} />}
      {!isDev && <ReactSwagger url={"/swagger.json"} />}
    </section>
  );
}
