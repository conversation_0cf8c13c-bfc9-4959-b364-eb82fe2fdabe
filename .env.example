# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Drizzle
DATABASE_URL="mysql://root:password@localhost:3306/hpsnz-web-video"

# Next Auth
# You can generate a new secret on the command line with:
# openssl rand -base64 32
# https://next-auth.js.org/configuration/options#secret
# NEXTAUTH_SECRET=""
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET=

# Next Auth Keycloak Provider
KEYCLOAK_ID="1"
KEYCLOAK_SECRET="1"
NEXT_PUBLIC_KEYCLOAK_ROOT=

# aws s3
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_BUCKET_NAME=
AWS_LAMBDA_CONVERTER_URL=

CLOUDFRONT_KEY_PAIR_ID=
CLOUDFRONT_PRIVATE_KEY=
CLOUDFRONT_DOMAIN=
# MUX
MUX_ACCESS_TOKEN_ID=
MUX_SECRET_KEY=
MUX_SIGNING_KEY_ID=
MUX_SIGNING_KEY_SECRET=
MUX_RESTRICTION_ID=

NEXT_PUBLIC_PTA_ROOT_URL=