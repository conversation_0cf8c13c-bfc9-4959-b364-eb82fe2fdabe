import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import { SortBy, UserRole, type Sport, type VideoStatus } from "~/lib/enums";
import { getUserSports } from "~/lib/roles";
import { db } from "~/server/db";
import { getHTTPStatusCodeFromError } from "~/server/utils/errorCode";
import { checkToken } from "~/server/utils/permissions";
import { getAthletes } from "~/server/utils/pta";
import { getVideos } from "~/server/utils/videos";

/**
 * @swagger
 * /api/v1/video:
 *   get:
 *     description: Get a list of videos summary.
 *     tags: [Get/list video(s)]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: The number of items to return, default 10
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: The page number
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, video_date]
 *         description: The page number
 *       - in: query
 *         name: sports
 *         schema:
 *           type: string
 *           enum: [cycling, kayak, rowing, sailing, snow_sports, "canoe_slalom", swimming, training, trampoline]
 *         description: Filter videos by sport (optional)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Raw, Trimmed, Tagged_by_AI, Tagged, Review, Analysed]
 *         description: Filter videos by status (optional)
 *       - in: query
 *         name: searchText
 *         schema:
 *           type: string
 *         description: Filter videos by search (optional)
 *     responses:
 *       200:
 *         description: A list of videos summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 videosCount:
 *                   type: number
 *                 videoList:
 *                   type: array
 *                   items:
 *                      type: object
 *                      properties:
 *                        id:
 *                          type: string
 *                        title:
 *                          type: string
 *                        sports:
 *                          type: string
 *                        permission:
 *                          type: string
 *                        path:
 *                          type: string
 *                        status:
 *                          type: string
 *                          enum: [Raw, Trimmed, Tagged_by_AI, AI_in_Progress, AI_Failed, Tagged, Review, Analysed]
 *                        duration:
 *                          type: number
 *                        fps:
 *                          type: number
 *                        isDraft:
 *                          type: boolean
 *                        isDeleted:
 *                          type: boolean
 *                        createdAt:
 *                          type: string
 *                        updatedAt:
 *                          type: string
 */

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const sportsFromParams = searchParams.getAll("sports") as
    | Sport[]
    | Sport
    | null
    | undefined;
  let sports = undefined as undefined | Sport[];
  if (sportsFromParams) {
    sports = Array.isArray(sportsFromParams)
      ? sportsFromParams
      : [sportsFromParams];
  }
  const searchText = searchParams.get("searchText") ?? undefined;
  const videoStatusesFromParams = searchParams.getAll("status") as
    | VideoStatus
    | VideoStatus[]
    | null
    | undefined;
  let videoStatuses = undefined as undefined | VideoStatus[];
  if (videoStatusesFromParams) {
    videoStatuses = Array.isArray(videoStatusesFromParams)
      ? videoStatusesFromParams
      : [videoStatusesFromParams];
  }
  const page = +(searchParams.get("page") ?? 0);
  const pageSize = +(searchParams.get("pageSize") ?? 10);
  let sortBy = searchParams.get("sortBy") as SortBy | null | undefined;
  if (!sortBy) {
    sortBy = SortBy.video_date;
  }

  try {
    const { tokenPayload, token } = await checkToken(request);
    const userSports = getUserSports(tokenPayload.roles);
    const isAdmin = tokenPayload.roles.includes(UserRole.admin);
    const userAthletes = await getAthletes({
      token,
      is_hp: true,
    });

    const videos = await getVideos({
      db,
      isAdmin,
      userSports,
      page,
      pageSize,
      searchText,
      sports,
      videoStatuses,
      isSummary: true,
      sortBy,
      subscribedAthletesId: userAthletes?.map((x) => x.athlete_id),
    });

    return Response.json({ count: videos.videosCount, list: videos.videoList });
  } catch (cause) {
    // console.log("cause", cause);
    if (cause instanceof TRPCError) {
      return Response.json(cause, {
        status: getHTTPStatusCodeFromError(cause.code),
      });
    }
    return Response.json(cause, {
      status: 500,
    });
  }
}
