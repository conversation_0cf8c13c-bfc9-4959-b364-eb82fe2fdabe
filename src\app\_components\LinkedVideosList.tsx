"use client";

import { api } from "~/trpc/react";
import Link from "next/link";
import Image from "next/image";
import { Skeleton } from "~/components/ui/skeleton";

interface VideoInfo {
  id: string;
  title: string;
  thumbnail?: string;
  playbackId?: string;
  relationshipType: string;
  isParent: boolean;
}

export const LinkedVideosList = ({
  currentVideoId,
}: {
  currentVideoId: string;
}) => {
  // Get current video info
  const { data: currentVideo } = api.video.get.useQuery(
    { id: currentVideoId },
    { enabled: !!currentVideoId },
  );

  // Get parent video details if current video is a child
  const { data: parentVideo } = api.video.get.useQuery(
    { id: currentVideo?.parentId ?? "" },
    { enabled: !!currentVideo?.parentId },
  );

  // Get linked videos (children) for the parent video (or current video if it's a parent)
  const parentId = parentVideo?.id ?? currentVideoId;
  const { data: linkedVideosData, isLoading: isLoadingLinkedVideos } =
    api.video.getLinkedVideos.useQuery(
      {
        parentId: parentId,
      },
      { enabled: !!parentId },
    );

  const linkedVideos = linkedVideosData?.videoList ?? [];

  // Build the videos list
  const videosToDisplay: VideoInfo[] = [];

  // If current video is a child, add parent at the top
  if (parentVideo) {
    videosToDisplay.push({
      id: parentVideo.id,
      title: parentVideo.title ?? "",
      thumbnail: parentVideo.thumbnail ?? undefined,
      playbackId: parentVideo.muxPlaybackId ?? undefined,
      relationshipType: "parent",
      isParent: true,
    });
  }

  // Add all children (excluding the current video)
  linkedVideos.forEach((video) => {
    if (video.id !== currentVideoId) {
      videosToDisplay.push({
        id: video.id,
        title: video.title ?? "",
        thumbnail: video.thumbnail ?? undefined,
        playbackId: video.muxPlaybackId ?? undefined,
        relationshipType: video.relationshipType ?? "",
        isParent: false,
      });
    }
  });

  if (isLoadingLinkedVideos) {
    return (
      <div className="mt-6 border-t border-grey pt-6">
        <h3 className="mb-4 text-lg font-semibold text-white">
          Loading Videos...
        </h3>
        <div className="flex flex-col gap-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex gap-3 rounded-md p-3">
              <div className="flex-shrink-0">
                <Skeleton className="h-32 w-48" />
              </div>
              <div className="flex flex-1 flex-col justify-between gap-2 rounded-md">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-3 w-16" />
                  <Skeleton className="h-3 w-12" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (videosToDisplay.length <= 0) {
    return (
      <div className="mt-6 border-t border-grey pt-6">
        <h3 className="mb-4 text-lg font-semibold text-white">
          No Related Videos
        </h3>
      </div>
    );
  }

  return (
    <div className="mt-6 border-t border-grey pt-6">
      <h3 className="mb-4 text-lg font-semibold text-white">Related Videos</h3>
      <div className="flex flex-col gap-3">
        {videosToDisplay.map((video) => (
          <Link
            key={video.id}
            href={`/videos/${video.id}`}
            className="flex gap-3 rounded-lg p-3 transition-colors hover:bg-grey"
          >
            <div className="flex-shrink-0">
              <div className="relative h-32 w-48 overflow-hidden rounded bg-grey">
                <Image
                  src={video.thumbnail ?? ""}
                  alt={video.title}
                  fill
                  sizes="192px"
                  className="object-cover"
                />
              </div>
            </div>

            <div className="flex flex-1 flex-col justify-between">
              <div>
                <h4 className="line-clamp-2 text-sm font-medium text-white">
                  {video.title}
                </h4>
                <div className="mt-1 flex items-center gap-2">
                  {video.isParent ? (
                    <span className="text-xs text-orange">Parent Video</span>
                  ) : (
                    <span className="text-xs capitalize text-white/80">
                      {video.relationshipType}
                    </span>
                  )}
                  {video.id === currentVideoId && (
                    <span className="text-xs text-green">Current</span>
                  )}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};
